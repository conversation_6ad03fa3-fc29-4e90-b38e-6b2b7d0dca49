<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      RayDesign Technologies | Elevating Businesses Through Digital Brilliance
    </title>
    <meta
      name="description"
      content="RayDesign Technologies - Your strategic transformation partner merging design thinking, intelligent automation, full-stack engineering, and marketing science to create high-impact digital ecosystems."
    />
    <meta
      name="keywords"
      content="web development, mobile app, UI/UX design, digital marketing, AI solutions, RPA, automation, branding, SEO, PPC, eCommerce, SaaS development"
    />
    <meta name="author" content="RayDesign Technologies" />

    <!-- Open Graph / Social Media Meta Tags -->
    <meta
      property="og:title"
      content="RayDesign Technologies | Digital Innovation Partner"
    />
    <meta
      property="og:description"
      content="Transforming business challenges into seamless digital experiences through innovative technology solutions and strategic digital marketing."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://raydesigntechnologies.com" />
    <meta
      property="og:image"
      content="https://raydesigntechnologies.com/images/og-image.jpg"
    />
    <meta property="og:site_name" content="RayDesign Technologies" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="RayDesign Technologies | Digital Innovation Partner"
    />
    <meta
      name="twitter:description"
      content="Your strategic partner for web development, mobile apps, digital marketing, and AI solutions."
    />
    <meta
      name="twitter:image"
      content="https://raydesigntechnologies.com/images/twitter-card.jpg"
    />

    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5" />
    <meta name="msapplication-TileColor" content="#6b46c1" />
    <meta name="theme-color" content="#6b46c1" />

    <!-- Canonical URL -->
    <meta rel="canonical" href="https://raydesigntechnologies.com" />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&family=Montserrat:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Animate.css -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />

    <!-- AOS - Animate On Scroll -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                50: "#f5f3ff",
                100: "#ede9fe",
                200: "#ddd6fe",
                300: "#c4b5fd",
                400: "#a78bfa",
                500: "#8b5cf6",
                600: "#7c3aed",
                700: "#6d28d9",
                800: "#5b21b6",
                900: "#4c1d95",
                950: "#2e1065",
              },
              secondary: {
                50: "#fdf2f8",
                100: "#fce7f3",
                200: "#fbcfe8",
                300: "#f9a8d4",
                400: "#f472b6",
                500: "#ec4899",
                600: "#db2777",
                700: "#be185d",
                800: "#9d174d",
                900: "#831843",
                950: "#500724",
              },
              dark: "#121212",
              light: "#f8f9fa",
            },
            fontFamily: {
              inter: ["Inter", "sans-serif"],
              poppins: ["Poppins", "sans-serif"],
              montserrat: ["Montserrat", "sans-serif"],
            },
            boxShadow: {
              glass: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
              neon: '0 0 5px theme("colors.primary.400"), 0 0 20px theme("colors.primary.700")',
              soft: "0 10px 50px -12px rgba(0, 0, 0, 0.25)",
            },
            backgroundImage: {
              "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
              "gradient-conic": "conic-gradient(var(--tw-gradient-stops))",
            },
            animation: {
              float: "float 6s ease-in-out infinite",
              "pulse-slow": "pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite",
              "bounce-slow": "bounce 3s infinite",
            },
            keyframes: {
              float: {
                "0%, 100%": { transform: "translateY(0)" },
                "50%": { transform: "translateY(-20px)" },
              },
            },
            borderRadius: {
              xl: "1rem",
              "2xl": "1.5rem",
              "3xl": "2rem",
              "4xl": "3rem",
            },
          },
        },
        variants: {
          extend: {
            opacity: ["group-hover"],
            transform: ["group-hover"],
            scale: ["group-hover"],
            translate: ["group-hover"],
          },
        },
      };
    </script>

    <style>
      /* Custom Styles */
      .bg-glass {
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.18);
      }

      .bg-glass-dark {
        background: rgba(17, 17, 17, 0.75);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.05);
      }

      .text-gradient {
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        background-image: linear-gradient(to right, #7c3aed, #db2777);
      }

      .bg-gradient-purple {
        background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
      }

      .bg-gradient-purple-pink {
        background: linear-gradient(135deg, #5b21b6 0%, #be185d 100%);
      }

      .bg-dots {
        background-image: radial-gradient(#6d28d9 1px, transparent 1px);
        background-size: 20px 20px;
      }

      /* Custom Animations */
      @keyframes glow {
        0%,
        100% {
          box-shadow: 0 0 15px rgba(139, 92, 246, 0.7);
        }
        50% {
          box-shadow: 0 0 30px rgba(139, 92, 246, 0.9);
        }
      }

      .animate-glow {
        animation: glow 3s ease-in-out infinite;
      }

      /* Custom Scrollbar */
      ::-webkit-scrollbar {
        width: 10px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      ::-webkit-scrollbar-thumb {
        background: #6d28d9;
        border-radius: 5px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #5b21b6;
      }

      /* Smooth Scrolling */
      html {
        scroll-behavior: smooth;
      }

      /* Curved Sections */
      .curve-top {
        position: relative;
      }

      .curve-top::before {
        content: "";
        position: absolute;
        top: -50px;
        left: 0;
        width: 100%;
        height: 50px;
        background: inherit;
        border-top-left-radius: 50% 100%;
        border-top-right-radius: 50% 100%;
      }

      .curve-bottom {
        position: relative;
      }

      .curve-bottom::after {
        content: "";
        position: absolute;
        bottom: -50px;
        left: 0;
        width: 100%;
        height: 50px;
        background: inherit;
        border-bottom-left-radius: 50% 100%;
        border-bottom-right-radius: 50% 100%;
      }

      /* Custom Utility Classes */
      .backdrop-blur {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
      }
    </style>
  </head>
  <body class="font-inter antialiased text-gray-800 overflow-x-hidden bg-white">
    <!-- Preloader -->
    <div
      id="preloader"
      class="fixed inset-0 z-50 flex items-center justify-center bg-white"
    >
      <div class="relative w-24 h-24">
        <div
          class="absolute top-0 left-0 w-full h-full rounded-full border-4 border-t-primary-600 border-r-primary-400 border-b-primary-200 border-l-transparent animate-spin"
        ></div>
        <img
          src="https://i.postimg.cc/cCwXxx3N/Ray-Design-Logo.png"
          alt="RayDesign Technologies"
          class="w-16 h-16 object-contain absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        />
      </div>
    </div>

    <!-- Notification Bar -->
    <div
      class="bg-gradient-purple text-white py-2 px-4 text-center relative overflow-hidden"
    >
      <div
        class="animate-marquee whitespace-nowrap flex items-center justify-center"
      >
        <span class="text-sm font-medium mx-4"
          >🎉 Limited Time Offer: Get a FREE 45-minute consultation with our
          experts!
          <a href="#contact" class="underline font-bold ml-2 hover:text-white"
            >Book Now</a
          ></span
        >
        <span class="mx-4">|</span>
        <span class="text-sm font-medium mx-4"
          >🚀 New AI-powered solutions that boost conversion rates by 300%
          <a href="#services" class="underline font-bold ml-2 hover:text-white"
            >Learn More</a
          ></span
        >
        <span class="mx-4">|</span>
        <span class="text-sm font-medium mx-4"
          >💼 Trusted by 500+ businesses worldwide
          <a
            href="#testimonials"
            class="underline font-bold ml-2 hover:text-white"
            >See Testimonials</a
          ></span
        >
      </div>
    </div>

    <!-- Header Navigation -->
    <header
      id="header"
      class="sticky top-0 z-40 w-full transition-all duration-300"
    >
      <nav class="bg-glass border-b border-gray-200 backdrop-blur-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-20">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
              <a href="/" class="flex items-center">
                <img
                  class="h-12 w-auto"
                  src="https://i.postimg.cc/cCwXxx3N/Ray-Design-Logo.png"
                  alt="RayDesign Technologies Logo"
                />
              </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:ml-6 md:flex md:items-center md:space-x-4">
              <a
                href="index.html"
                class="px-3 py-2 rounded-md text-sm font-medium text-primary-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group"
              >
                Home
                <span
                  class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                ></span>
              </a>
              <a
                href="about.html"
                class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group"
              >
                About Us
                <span
                  class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                ></span>
              </a>

              <!-- Services Dropdown -->
              <div class="relative group">
                <button
                  class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 inline-flex items-center group-hover:text-primary-700"
                >
                  Services
                  <svg
                    class="ml-1 w-4 h-4 transition-transform duration-300 group-hover:rotate-180"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                  <span
                    class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                  ></span>
                </button>

                <!-- Dropdown Menu -->
                <div
                  class="absolute left-0 mt-2 w-72 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform origin-top-left group-hover:translate-y-0 translate-y-2 z-50 border border-gray-100"
                >
                  <div class="p-4 grid grid-cols-1 gap-2">
                    <div class="col-span-1">
                      <h3
                        class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                      >
                        Web & Mobile
                      </h3>
                      <a
                        href="services/web-development.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-laptop-code mr-3 text-primary-500"></i>
                        Web Development
                      </a>
                      <a
                        href="services/mobile-apps.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-mobile-alt mr-3 text-primary-500"></i>
                        Mobile Applications
                      </a>
                      <a
                        href="services/e-commerce.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i
                          class="fas fa-shopping-cart mr-3 text-primary-500"
                        ></i>
                        E-Commerce Solutions
                      </a>
                    </div>
                  </div>

                  <div
                    class="p-4 border-t border-gray-100 grid grid-cols-1 gap-2"
                  >
                    <div class="col-span-1">
                      <h3
                        class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                      >
                        Marketing
                      </h3>
                      <a
                        href="services/social-media.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-share-alt mr-3 text-primary-500"></i>
                        Social Media Marketing
                      </a>
                      <a
                        href="services/seo.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-search mr-3 text-primary-500"></i>
                        SEO Services
                      </a>
                      <a
                        href="services/ppc.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-bullseye mr-3 text-primary-500"></i>
                        PPC Advertising
                      </a>
                    </div>
                  </div>

                  <div
                    class="p-4 border-t border-gray-100 grid grid-cols-1 gap-2"
                  >
                    <div class="col-span-1">
                      <h3
                        class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                      >
                        Design & Innovation
                      </h3>
                      <a
                        href="services/ui-ux-design.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i
                          class="fas fa-pencil-ruler mr-3 text-primary-500"
                        ></i>
                        UI/UX Design
                      </a>
                      <a
                        href="services/branding.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-paint-brush mr-3 text-primary-500"></i>
                        Branding & Identity
                      </a>
                    </div>
                  </div>

                  <div
                    class="p-4 border-t border-gray-100 grid grid-cols-1 gap-2"
                  >
                    <div class="col-span-1">
                      <h3
                        class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                      >
                        AI & Automation
                      </h3>
                      <a
                        href="services/ai-solutions.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-robot mr-3 text-primary-500"></i>
                        AI Solutions
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <a
                href="portfolio.html"
                class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group"
              >
                Portfolio
                <span
                  class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                ></span>
              </a>

              <a
                href="contact.html"
                class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group"
              >
                Contact
                <span
                  class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                ></span>
              </a>
            </div>

            <!-- Action Buttons -->
            <div class="hidden md:flex items-center">
              <a
                href="#contact"
                class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 shadow-md transition-all duration-300 ml-3"
              >
                Get a Quote
              </a>
              <a
                href="tel:+447878361409"
                class="inline-flex items-center justify-center px-4 py-2 border border-primary-600 text-sm font-medium rounded-md text-primary-600 bg-transparent hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 ml-3"
              >
                <i class="fas fa-phone-alt mr-2"></i> Call Us
              </a>
            </div>

            <!-- Mobile menu button -->
            <div class="flex items-center md:hidden">
              <button
                id="mobile-menu-button"
                type="button"
                class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
                aria-expanded="false"
              >
                <span class="sr-only">Open main menu</span>
                <svg
                  id="menu-icon"
                  class="block h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
                <svg
                  id="close-icon"
                  class="hidden h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile menu, show/hide based on menu state -->
        <div id="mobile-menu" class="hidden md:hidden">
          <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white">
            <a
              href="index.html"
              class="block px-3 py-2 rounded-md text-base font-medium text-primary-700 bg-primary-50"
              >Home</a
            >
            <a
              href="about.html"
              class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >About Us</a
            >

            <!-- Mobile Services Accordion -->
            <div class="relative">
              <button
                id="mobile-services-button"
                class="w-full flex justify-between items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >
                Services
                <svg
                  id="mobile-services-icon"
                  class="h-5 w-5 transform transition-transform duration-200"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              <div
                id="mobile-services-dropdown"
                class="hidden px-4 py-2 space-y-1"
              >
                <div class="py-2">
                  <h4
                    class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                  >
                    Web & Mobile
                  </h4>
                  <a
                    href="services/web-development.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >Web Development</a
                  >
                  <a
                    href="services/mobile-apps.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >Mobile Applications</a
                  >
                  <a
                    href="services/e-commerce.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >E-Commerce Solutions</a
                  >
                </div>

                <div class="py-2 border-t border-gray-100">
                  <h4
                    class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                  >
                    Marketing
                  </h4>
                  <a
                    href="services/social-media.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >Social Media Marketing</a
                  >
                  <a
                    href="services/seo.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >SEO Services</a
                  >
                  <a
                    href="services/ppc.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >PPC Advertising</a
                  >
                </div>

                <div class="py-2 border-t border-gray-100">
                  <h4
                    class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                  >
                    Design & Innovation
                  </h4>
                  <a
                    href="services/ui-ux-design.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >UI/UX Design</a
                  >
                  <a
                    href="services/branding.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >Branding & Identity</a
                  >
                </div>

                <div class="py-2 border-t border-gray-100">
                  <h4
                    class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                  >
                    AI & Automation
                  </h4>
                  <a
                    href="/services/ai-solutions.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >AI Solutions</a
                  >
                </div>

                <div class="py-2 border-t border-gray-100">
                  <a
                    href="/services.html"
                    class="flex items-center px-3 py-2 text-sm font-medium text-primary-700 hover:text-primary-900"
                  >
                    View all services
                    <svg
                      class="ml-1 w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <a href="portfolio.html" class="block px-3 py-2 rounded-md text-base
            font-medium text-gray-700 hover:bg-primary-50
            hover:text-primary-700" Portfolio / >

            <a
              href="contact.html"
              class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >Contact</a
            >
          </div>

          <div class="pt-4 pb-3 border-t border-gray-200">
            <div class="flex items-center px-4">
              <div class="flex-shrink-0">
                <i class="fas fa-headset text-2xl text-primary-600"></i>
              </div>
              <div class="ml-3">
                <div class="text-base font-medium text-gray-800">
                  Need help?
                </div>
                <div class="text-sm font-medium text-gray-500">
                  Our experts are available
                </div>
              </div>
            </div>
            <div class="mt-3 space-y-1 px-2">
              <a
                href="tel:+447878361409"
                class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >
                <i class="fas fa-phone-alt mr-2"></i> +44 7878 361409
              </a>
              <a
                href="mailto:<EMAIL>@raydesign.uk"
                class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >
                <i class="fas fa-envelope mr-2"></i>
                <EMAIL>@raydesign.uk
              </a>
              <a
                href="#contact"
                class="block px-3 py-2 rounded-md text-base font-medium bg-primary-600 text-white hover:bg-primary-700 mt-3 text-center"
              >
                Get a Quote
              </a>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <!-- JavaScript for Header Interactions -->
    <script>
      // Handle header background change on scroll
      const header = document.getElementById("header");
      window.addEventListener("scroll", () => {
        if (window.scrollY > 10) {
          header.classList.add("bg-white", "shadow-md");
          header.classList.remove("bg-transparent");
        } else {
          header.classList.remove("bg-white", "shadow-md");
          header.classList.add("bg-transparent");
        }
      });

      // Mobile menu toggle
      const mobileMenuButton = document.getElementById("mobile-menu-button");
      const mobileMenu = document.getElementById("mobile-menu");
      const menuIcon = document.getElementById("menu-icon");
      const closeIcon = document.getElementById("close-icon");

      mobileMenuButton.addEventListener("click", () => {
        mobileMenu.classList.toggle("hidden");
        menuIcon.classList.toggle("hidden");
        closeIcon.classList.toggle("hidden");
      });

      // Mobile services dropdown toggle
      const mobileServicesButton = document.getElementById(
        "mobile-services-button"
      );
      const mobileServicesDropdown = document.getElementById(
        "mobile-services-dropdown"
      );
      const mobileServicesIcon = document.getElementById(
        "mobile-services-icon"
      );

      mobileServicesButton.addEventListener("click", () => {
        mobileServicesDropdown.classList.toggle("hidden");
        mobileServicesIcon.classList.toggle("rotate-180");
      });

      // Preloader
      window.addEventListener("load", () => {
        const preloader = document.getElementById("preloader");
        setTimeout(() => {
          preloader.classList.add("opacity-0");
          setTimeout(() => {
            preloader.style.display = "none";
          }, 500);
        }, 1000);
      });
    </script>
    <!-- Hero Section with Animated Elements -->
    <section class="relative overflow-hidden bg-white py-16 md:py-20 lg:py-28">
      <!-- Background Decoration Elements -->
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
        <!-- Animated Gradient Circles -->
        <div
          class="absolute top-10 right-10 w-64 h-64 bg-gradient-to-r from-primary-300 to-primary-600 rounded-full filter blur-3xl opacity-20 animate-pulse-slow"
        ></div>
        <div
          class="absolute -bottom-20 -left-20 w-80 h-80 bg-gradient-to-r from-secondary-300 to-secondary-600 rounded-full filter blur-3xl opacity-20 animate-float"
        ></div>
        <div
          class="absolute top-1/3 left-1/4 w-40 h-40 bg-gradient-to-r from-purple-300 to-indigo-500 rounded-full filter blur-3xl opacity-20 animate-bounce-slow"
        ></div>

        <!-- Dot Pattern Background -->
        <div class="absolute inset-0 bg-dots opacity-10"></div>

        <!-- SVG Wave Shape -->
        <svg
          class="absolute bottom-0 left-0 w-full"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
        >
          <path
            fill="rgba(139, 92, 246, 0.05)"
            fill-opacity="1"
            d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,202.7C672,203,768,181,864,181.3C960,181,1056,203,1152,197.3C1248,192,1344,160,1392,144L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>

        <!-- Abstract SVG Shapes -->
        <svg
          class="absolute top-20 right-1/3 w-24 h-24 text-primary-100 opacity-50 transform rotate-12 animate-float"
          viewBox="0 0 200 200"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill="currentColor"
            d="M37.5,-65.1C50.2,-58.9,63.1,-52.3,71.6,-41.5C80.1,-30.6,84.2,-15.3,83.6,-0.3C83,14.6,77.8,29.2,69.1,41.2C60.5,53.2,48.4,62.6,35.1,68.3C21.8,74,10.9,76,0.1,75.8C-10.7,75.7,-21.4,73.4,-33.4,69.2C-45.4,65,-58.8,58.8,-69.4,48.5C-80,38.1,-87.9,23.5,-89.4,8.1C-90.9,-7.3,-86,-23.5,-78.4,-38.2C-70.7,-53,-60.2,-66.3,-46.6,-72.1C-33,-78,-16.5,-76.4,-2.3,-72.3C11.9,-68.2,23.9,-61.5,37.5,-65.1Z"
            transform="translate(100 100)"
          />
        </svg>

        <svg
          class="absolute bottom-20 left-20 w-20 h-20 text-secondary-200 opacity-70 transform -rotate-12 animate-float"
          viewBox="0 0 200 200"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill="currentColor"
            d="M44.3,-76.5C58.4,-71.2,71.3,-61.3,79.4,-48.1C87.5,-34.8,90.8,-18.4,90.3,-2.4C89.8,13.5,85.6,27.1,77.2,38.2C68.9,49.3,56.3,58,43.1,65.5C29.9,73,14.9,79.4,0.3,78.9C-14.3,78.5,-28.7,71.3,-42.7,63.3C-56.7,55.3,-70.4,46.4,-78.8,33.5C-87.2,20.6,-90.3,3.6,-87.4,-12.1C-84.5,-27.8,-75.5,-42.2,-63.9,-53.4C-52.3,-64.5,-38.1,-72.3,-23.6,-76.9C-9.1,-81.5,5.7,-82.9,19.4,-80.8C33.1,-78.7,45.7,-73.2,44.3,-76.5Z"
            transform="translate(100 100)"
          />
        </svg>
      </div>

      <div class="relative container mx-auto px-4 sm:px-6 lg:px-8 z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- Left Column: Hero Text Content -->
          <div
            class="lg:pr-10 order-2 lg:order-1"
            data-aos="fade-right"
            data-aos-delay="100"
            data-aos-duration="1000"
          >
            <h5
              class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4 animate__animated animate__fadeInDown"
            >
              <span class="flex items-center">
                <span
                  class="w-2 h-2 rounded-full bg-primary-600 mr-2 animate-pulse"
                ></span>
                Future-Forging Innovation Powerhouse
              </span>
            </h5>

            <h1
              class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-gray-900 mb-6 animate__animated animate__fadeInUp"
            >
              <span class="text-gradient">Elevating</span> Businesses Through
              Digital
              <span class="relative">
                Brilliance
                <svg
                  class="absolute bottom-0 left-0 w-full"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 500 150"
                  preserveAspectRatio="none"
                >
                  <path
                    d="M7.7,145.6C109,125,299.9,116.2,401,121.3c42.1,2.2,87.6,11.8,87.3,25.7"
                    stroke="#8b5cf6"
                    stroke-width="4"
                    fill="none"
                    stroke-linecap="round"
                  />
                </svg>
              </span>
            </h1>

            <p
              class="text-xl text-gray-600 mb-8 animate__animated animate__fadeInUp animate__delay-1s"
            >
              We merge design thinking, intelligent automation, full-stack
              engineering, and marketing science to architect high-impact,
              human-centered digital ecosystems that fuel business growth.
            </p>

            <div
              class="flex flex-wrap gap-4 mb-8 animate__animated animate__fadeInUp animate__delay-2s"
            >
              <a
                href="#contact"
                class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <span>Start Your Project</span>
                <svg
                  class="ml-2 -mr-1 w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </a>

              <a
                href="#services"
                class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-primary-700 bg-white border border-primary-200 hover:bg-primary-50 shadow-md hover:shadow-lg transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <span>Explore Services</span>
                <svg
                  class="ml-2 -mr-1 w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </a>
            </div>
            <style>
              .alt-image {
                height: 2rem; /* same as h-8 */
                width: 120px; /* match image width */
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.875rem; /* Tailwind text-sm */
                font-weight: 500;
                color: #666;
                border: 1px dashed #ccc;
                border-radius: 0.375rem; /* rounded-md */
                background-color: transparent;
                transition: all 0.3s ease-in-out;
                filter: grayscale(100%);
                opacity: 0.7;
              }

              .alt-image:hover {
                filter: grayscale(0%);
                opacity: 1;
                color: #111;
              }

              .alt-image::before {
                content: attr(data-alt);
              }
            </style>
            <!-- Trust Badges -->
            <div
              class="flex flex-col space-y-4 animate__animated animate__fadeInUp animate__delay-3s"
            >
              <p class="text-sm text-gray-500 flex items-center">
                <svg
                  class="w-4 h-4 mr-2 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                Trusted by 500+ businesses worldwide
              </p>

              <div class="flex flex-wrap items-center gap-6">
                <div class="alt-image" data-alt="ACME Inc"></div>
                <div class="alt-image" data-alt="TechCorp"></div>
                <div class="alt-image" data-alt="GlobalSys"></div>
                <div class="alt-image" data-alt="Innovex"></div>
              </div>

              <!-- Rating Stars -->
              <div class="flex items-center pt-2">
                <div class="flex items-center">
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <svg
                    class="w-5 h-5 text-gray-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <p class="ml-2 text-sm text-gray-600">
                    <span class="font-bold text-gray-900">4.9</span> out of 5
                  </p>
                </div>
                <span class="mx-2 text-gray-300">|</span>
                <p class="text-sm text-gray-600">
                  <span class="font-bold text-gray-900">250+</span> reviews
                </p>
              </div>
            </div>
          </div>
          <style>
            @media only screen and (max-width: 768px) {
              #right-column {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                pointer-events: none !important;
                position: absolute !important;
                height: 0 !important;
                width: 0 !important;
                overflow: hidden !important;
              }
            }
          </style>
          <!-- Right Column: Hero Image/Animation -->
          <div
            class="lg:pl-10 order-1 lg:order-2 relative"
            id="right-column"
            data-aos="fade-left"
            data-aos-delay="300"
            data-aos-duration="1000"
          >
            <div class="relative">
              <!-- Main Hero Image with Glassmorphism Frame -->
              <div class="bg-white p-2 rounded-2xl shadow-xl animate-float">
                <div class="relative overflow-hidden rounded-xl shadow-lg">
                  <img
                    src="https://images.unsplash.com/photo-1618761714954-0b8cd0026356?auto=format&fit=crop&w=800&q=80"
                    alt="Digital Transformation"
                    class="w-full h-auto object-cover rounded-xl"
                  />

                  <!-- Overlay with Gradient -->
                  <div
                    class="absolute inset-0 bg-gradient-to-tr from-primary-900/60 to-transparent"
                  ></div>

                  <!-- Floating UI Elements on the Image -->
                  <div
                    class="absolute top-10 right-10 bg-glass p-4 rounded-xl shadow-lg backdrop-blur-md animate__animated animate__fadeInRight"
                  >
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <p class="text-white font-medium">
                        Project Status: Active
                      </p>
                    </div>
                    <div
                      class="mt-2 w-36 h-1 bg-white/20 rounded-full overflow-hidden"
                    >
                      <div
                        class="w-3/4 h-full bg-primary-500 rounded-full animate-pulse"
                      ></div>
                    </div>
                  </div>

                  <div
                    class="absolute bottom-10 left-10 bg-white/90 backdrop-blur-md p-4 rounded-xl shadow-lg max-w-xs animate__animated animate__fadeInUp"
                  >
                    <div class="flex items-center mb-2">
                      <div
                        class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3"
                      >
                        <svg
                          class="w-4 h-4 text-primary-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </div>
                      <h3 class="font-bold text-gray-800">Recent Success</h3>
                    </div>
                    <p class="text-sm text-gray-600">
                      E-commerce conversion rate increased by 210% after our UX
                      redesign and AI-driven recommendations.
                    </p>
                  </div>
                </div>
              </div>

              <!-- Floating Elements Around Main Image -->
              <div
                class="absolute -top-10 -right-10 bg-primary-50 p-4 rounded-lg shadow-lg animate-float"
                style="animation-delay: 1s"
              >
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3"
                  >
                    <i class="fas fa-chart-line text-primary-600"></i>
                  </div>
                  <div>
                    <p class="text-xs text-gray-500">Conversion Rate</p>
                    <p class="font-bold text-gray-800">+173%</p>
                  </div>
                </div>
              </div>

              <div
                class="absolute -bottom-8 -left-8 bg-white p-4 rounded-lg shadow-lg animate-float"
                style="animation-delay: 2s"
              >
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 bg-secondary-100 rounded-full flex items-center justify-center mr-3"
                  >
                    <i class="fas fa-users text-secondary-600"></i>
                  </div>
                  <div>
                    <p class="text-xs text-gray-500">Client Satisfaction</p>
                    <p class="font-bold text-gray-800">98%</p>
                  </div>
                </div>
              </div>

              <!-- Tech Stack Icons -->
              <div class="absolute -bottom-4 right-20 flex space-x-2">
                <div
                  class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center animate-bounce-slow"
                  style="animation-delay: 0s"
                >
                  <i class="fab fa-react text-blue-500"></i>
                </div>
                <div
                  class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center animate-bounce-slow"
                  style="animation-delay: 0.2s"
                >
                  <i class="fab fa-node-js text-green-600"></i>
                </div>
                <div
                  class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center animate-bounce-slow"
                  style="animation-delay: 0.4s"
                >
                  <i class="fab fa-aws text-orange-500"></i>
                </div>
                <div
                  class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center animate-bounce-slow"
                  style="animation-delay: 0.6s"
                >
                  <i class="fab fa-figma text-purple-500"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Bottom Section: Quick Stats/Features -->
        <div
          class="mt-20 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate__animated animate__fadeInUp"
          data-aos="fade-up"
          data-aos-delay="500"
          data-aos-duration="1000"
        >
          <div
            class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-shadow duration-300 border border-gray-100 transform hover:-translate-y-1 transition-transform duration-300"
          >
            <div
              class="rounded-full w-12 h-12 flex items-center justify-center bg-primary-100 text-primary-600 mb-4"
            >
              <svg
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                ></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              Strategic Approach
            </h3>
            <p class="text-gray-600">
              Data-driven strategies that align with your business goals and
              user needs.
            </p>
          </div>

          <div
            class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-shadow duration-300 border border-gray-100 transform hover:-translate-y-1 transition-transform duration-300"
          >
            <div
              class="rounded-full w-12 h-12 flex items-center justify-center bg-secondary-100 text-secondary-600 mb-4"
            >
              <svg
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                ></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              Creative Innovation
            </h3>
            <p class="text-gray-600">
              Cutting-edge designs and innovative solutions that set you apart
              from competitors.
            </p>
          </div>

          <div
            class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-shadow duration-300 border border-gray-100 transform hover:-translate-y-1 transition-transform duration-300"
          >
            <div
              class="rounded-full w-12 h-12 flex items-center justify-center bg-green-100 text-green-600 mb-4"
            >
              <svg
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                ></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              Secure & Reliable
            </h3>
            <p class="text-gray-600">
              Enterprise-grade security and performance that you can trust and
              depend on.
            </p>
          </div>

          <div
            class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-shadow duration-300 border border-gray-100 transform hover:-translate-y-1 transition-transform duration-300"
          >
            <div
              class="rounded-full w-12 h-12 flex items-center justify-center bg-blue-100 text-blue-600 mb-4"
            >
              <svg
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                ></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              Rapid Deployment
            </h3>
            <p class="text-gray-600">
              Agile methodologies that deliver results quickly while maintaining
              quality.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Contact CTA -->
    <div class="fixed bottom-6 right-6 z-40">
      <div class="relative group">
        <button
          class="w-16 h-16 rounded-full bg-gradient-purple text-white flex items-center justify-center shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 animate-pulse-slow group-hover:animate-none transition-all duration-300"
        >
          <svg
            class="w-7 h-7 transform group-hover:rotate-0 rotate-45 transition-transform duration-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            ></path>
          </svg>
        </button>

        <div
          class="absolute bottom-full right-0 mb-3 w-64 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300"
        >
          <div class="bg-white rounded-lg shadow-xl p-4">
            <div class="flex flex-col space-y-2">
              <a
                href="tel:+447878361409"
                class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-primary-50 rounded-md transition-colors duration-150"
              >
                <i class="fas fa-phone-alt text-primary-600 mr-3"></i>
                +44 7878 361409
              </a>
              <a
                href="mailto:<EMAIL>"
                class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-primary-50 rounded-md transition-colors duration-150"
              >
                <i class="fas fa-envelope text-primary-600 mr-3"></i>
                Send Email
              </a>
              <a
                href="#contact"
                class="flex items-center px-3 py-2 text-sm font-medium bg-primary-600 text-white hover:bg-primary-700 rounded-md transition-colors duration-150"
              >
                <i class="fas fa-comment-dots mr-3"></i>
                Start Chat
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Services Section with Interactive Cards -->
    <section id="services" class="py-20 bg-gray-50 relative overflow-hidden">
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <div
          class="absolute top-0 right-0 w-full h-32 bg-gradient-to-b from-white to-transparent"
        ></div>
        <div
          class="absolute bottom-0 right-0 w-full h-32 bg-gradient-to-t from-white to-transparent"
        ></div>

        <!-- SVG Background Pattern -->
        <svg
          class="absolute top-0 left-0 w-full opacity-10"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
        >
          <path
            fill="#6d28d9"
            fill-opacity="1"
            d="M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>

        <!-- Animated Dots Background -->
        <div class="absolute inset-0 bg-dots opacity-10"></div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-16" data-aos="fade-up">
          <span
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
          >
            Our Services
          </span>
          <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">
            Transforming Your Digital
            <span class="text-gradient">Presence</span>
          </h2>
          <p class="text-xl text-gray-600">
            We offer a comprehensive suite of digital services to elevate your
            business and drive meaningful results.
          </p>
        </div>

        <!-- Services Filter Tabs -->
        <div
          class="flex flex-wrap justify-center gap-2 mb-12"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          <button
            class="service-filter-btn active px-5 py-2 rounded-full text-sm font-medium bg-primary-600 text-white hover:bg-primary-700 transition-all duration-300 shadow-md"
            data-filter="all"
          >
            All Services
          </button>
          <button
            class="service-filter-btn px-5 py-2 rounded-full text-sm font-medium bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-700 transition-all duration-300 shadow-md"
            data-filter="web"
          >
            Web & Mobile
          </button>
          <button
            class="service-filter-btn px-5 py-2 rounded-full text-sm font-medium bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-700 transition-all duration-300 shadow-md"
            data-filter="marketing"
          >
            Digital Marketing
          </button>
          <button
            class="service-filter-btn px-5 py-2 rounded-full text-sm font-medium bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-700 transition-all duration-300 shadow-md"
            data-filter="design"
          >
            UI/UX & Branding
          </button>
          <button
            class="service-filter-btn px-5 py-2 rounded-full text-sm font-medium bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-700 transition-all duration-300 shadow-md"
            data-filter="ai"
          >
            AI & Automation
          </button>
        </div>

        <!-- Services Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <!-- Service Card 1 -->
          <div
            class="service-card group"
            data-category="web"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 h-full"
            >
              <div class="relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1547658719-da2b51169166?auto=format&fit=crop&w=800&q=80"
                  alt="Web Development"
                  class="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent"
                ></div>
                <div
                  class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
                >
                  <i class="fas fa-laptop-code text-primary-600 text-xl"></i>
                </div>
              </div>

              <div class="p-6">
                <h3
                  class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300"
                >
                  Custom Website Design & Development
                </h3>

                <p class="text-gray-600 mb-4">
                  Brand-centric, SEO-optimized websites that convert visitors
                  into customers with intuitive user experiences.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Responsive Design</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >SEO Optimization</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Custom CMS</span
                  >
                </div>

                <div class="pt-4 border-t border-gray-100">
                  <a
                    href="services/web-development.html"
                    class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                  >
                    Learn More
                    <svg
                      class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Service Card 2 -->
          <div
            class="service-card group"
            data-category="web"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 h-full"
            >
              <div class="relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1551650975-87deedd944c3?auto=format&fit=crop&w=800&q=80"
                  alt="Mobile App Development"
                  class="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent"
                ></div>
                <div
                  class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
                >
                  <i class="fas fa-mobile-alt text-primary-600 text-xl"></i>
                </div>
              </div>

              <div class="p-6">
                <h3
                  class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300"
                >
                  Mobile Application Development
                </h3>

                <p class="text-gray-600 mb-4">
                  Native and cross-platform apps for iOS and Android that
                  deliver exceptional user experiences with cutting-edge
                  technology.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >iOS</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Android</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Flutter</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >React Native</span
                  >
                </div>

                <div class="pt-4 border-t border-gray-100">
                  <a
                    href="services/mobile-apps.html"
                    class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                  >
                    Learn More
                    <svg
                      class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Service Card 3 -->
          <div
            class="service-card group"
            data-category="web"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 h-full"
            >
              <div class="relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1556740738-b6a63e27c4df?auto=format&fit=crop&w=800&q=80"
                  alt="E-Commerce Development"
                  class="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent"
                ></div>
                <div
                  class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
                >
                  <i class="fas fa-shopping-cart text-primary-600 text-xl"></i>
                </div>
              </div>

              <div class="p-6">
                <h3
                  class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300"
                >
                  E-Commerce Engineering
                </h3>

                <p class="text-gray-600 mb-4">
                  Intelligent online stores built on Shopify, WooCommerce,
                  Magento or custom platforms with conversion-focused features.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Shopify</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >WooCommerce</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Payment Gateways</span
                  >
                </div>

                <div class="pt-4 border-t border-gray-100">
                  <a
                    href="services/e-commerce.html"
                    class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                  >
                    Learn More
                    <svg
                      class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Service Card 4 -->
          <div
            class="service-card group"
            data-category="marketing"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 h-full"
            >
              <div class="relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a?auto=format&fit=crop&w=800&q=80"
                  alt="SEO Services"
                  class="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent"
                ></div>
                <div
                  class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
                >
                  <i class="fas fa-search text-primary-600 text-xl"></i>
                </div>
              </div>

              <div class="p-6">
                <h3
                  class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300"
                >
                  SEO & Search Visibility
                </h3>

                <p class="text-gray-600 mb-4">
                  Comprehensive search engine optimization strategies that
                  improve rankings, drive organic traffic, and increase
                  conversions.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Keyword Research</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >On-Page SEO</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Link Building</span
                  >
                </div>

                <div class="pt-4 border-t border-gray-100">
                  <a
                    href="/services/seo/"
                    class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                  >
                    Learn More
                    <svg
                      class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Service Card 5 -->
          <div
            class="service-card group"
            data-category="marketing"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 h-full"
            >
              <div class="relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&w=800&q=80"
                  alt="PPC Advertising"
                  class="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent"
                ></div>
                <div
                  class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
                >
                  <i class="fas fa-bullhorn text-primary-600 text-xl"></i>
                </div>
              </div>

              <div class="p-6">
                <h3
                  class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300"
                >
                  Performance Advertising
                </h3>

                <p class="text-gray-600 mb-4">
                  Strategic paid advertising campaigns across Google, Meta,
                  LinkedIn, and other platforms that maximize ROI and
                  conversions.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Google Ads</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Facebook Ads</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >LinkedIn Ads</span
                  >
                </div>

                <div class="pt-4 border-t border-gray-100">
                  <a
                    href="/services/ppc/"
                    class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                  >
                    Learn More
                    <svg
                      class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Service Card 6 -->
          <div
            class="service-card group"
            data-category="marketing"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 h-full"
            >
              <div class="relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1523575166472-a83a0ed1d522?auto=format&fit=crop&w=800&q=80"
                  alt="Social Media Marketing"
                  class="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent"
                ></div>
                <div
                  class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
                >
                  <i class="fas fa-hashtag text-primary-600 text-xl"></i>
                </div>
              </div>

              <div class="p-6">
                <h3
                  class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300"
                >
                  Social Media Management
                </h3>

                <p class="text-gray-600 mb-4">
                  Platform-native storytelling, community building, and content
                  strategies that build brand awareness and drive engagement.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Content Creation</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Community Management</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Analytics</span
                  >
                </div>

                <div class="pt-4 border-t border-gray-100">
                  <a
                    href="/services/social-media/"
                    class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                  >
                    Learn More
                    <svg
                      class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Service Card 7 -->
          <div
            class="service-card group"
            data-category="design"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 h-full"
            >
              <div class="relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1561070791-2526d30994b5?auto=format&fit=crop&w=800&q=80"
                  alt="UI/UX Design"
                  class="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent"
                ></div>
                <div
                  class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
                >
                  <i class="fas fa-pencil-ruler text-primary-600 text-xl"></i>
                </div>
              </div>

              <div class="p-6">
                <h3
                  class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300"
                >
                  UI/UX Design & Research
                </h3>

                <p class="text-gray-600 mb-4">
                  Human-centered design that creates intuitive, accessible, and
                  delightful digital experiences based on real user insights.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Wireframing</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Prototyping</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >User Testing</span
                  >
                </div>

                <div class="pt-4 border-t border-gray-100">
                  <a
                    href="/services/ui-ux-design/"
                    class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                  >
                    Learn More
                    <svg
                      class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Service Card 8 -->
          <div
            class="service-card group"
            data-category="design"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 h-full"
            >
              <div class="relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1603145733146-ae562a55031e?auto=format&fit=crop&w=800&q=80"
                  alt="Branding & Identity"
                  class="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent"
                ></div>
                <div
                  class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
                >
                  <i class="fas fa-paint-brush text-primary-600 text-xl"></i>
                </div>
              </div>

              <div class="p-6">
                <h3
                  class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300"
                >
                  Branding & Identity
                </h3>

                <p class="text-gray-600 mb-4">
                  Comprehensive brand development including logos, visual
                  systems, and guidelines that communicate your unique value
                  proposition.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Logo Design</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Brand Guidelines</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Visual Identity</span
                  >
                </div>

                <div class="pt-4 border-t border-gray-100">
                  <a
                    href="/services/branding/"
                    class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                  >
                    Learn More
                    <svg
                      class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Service Card 9 -->
          <div
            class="service-card group"
            data-category="ai"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 h-full"
            >
              <div class="relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1535378917042-10a22c95931a?auto=format&fit=crop&w=800&q=80"
                  alt="AI Solutions"
                  class="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/70 to-transparent"
                ></div>
                <div
                  class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md"
                >
                  <i class="fas fa-robot text-primary-600 text-xl"></i>
                </div>
              </div>

              <div class="p-6">
                <h3
                  class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300"
                >
                  AI & Machine Learning Solutions
                </h3>

                <p class="text-gray-600 mb-4">
                  Custom AI applications, chatbots, and data intelligence
                  solutions that automate processes and drive business insights.
                </p>

                <div class="flex flex-wrap gap-2 mb-4">
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Chatbots</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >Predictive Analytics</span
                  >
                  <span
                    class="px-3 py-1 text-xs font-medium text-primary-700 bg-primary-50 rounded-full"
                    >NLP</span
                  >
                </div>

                <div class="pt-4 border-t border-gray-100">
                  <a
                    href="/services/ai-solutions/"
                    class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                  >
                    Learn More
                    <svg
                      class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- View All Services Button -->
        <div class="text-center" data-aos="fade-up" data-aos-delay="400">
          <a
            href="services.html"
            class="inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <span>View All Services</span>
            <svg
              class="ml-2 -mr-1 w-5 h-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </a>
        </div>
      </div>

      <!-- Featured Service Showcase -->
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div
          class="relative overflow-hidden rounded-3xl bg-gradient-purple-pink p-8 md:p-12 lg:p-16 shadow-xl"
        >
          <div class="absolute top-0 right-0 w-full h-full overflow-hidden">
            <!-- Decorative Elements -->
            <div
              class="absolute top-0 right-0 w-64 h-64 bg-white rounded-full opacity-10 transform translate-x-1/3 -translate-y-1/3"
            ></div>
            <div
              class="absolute bottom-0 left-0 w-64 h-64 bg-white rounded-full opacity-10 transform -translate-x-1/3 translate-y-1/3"
            ></div>

            <!-- Abstract SVG Shape -->
            <svg
              class="absolute bottom-0 right-0 w-96 h-96 text-white opacity-10"
              viewBox="0 0 200 200"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill="currentColor"
                d="M37.5,-65.1C50.2,-58.9,63.1,-52.3,71.6,-41.5C80.1,-30.6,84.2,-15.3,83.6,-0.3C83,14.6,77.8,29.2,69.1,41.2C60.5,53.2,48.4,62.6,35.1,68.3C21.8,74,10.9,76,0.1,75.8C-10.7,75.7,-21.4,73.4,-33.4,69.2C-45.4,65,-58.8,58.8,-69.4,48.5C-80,38.1,-87.9,23.5,-89.4,8.1C-90.9,-7.3,-86,-23.5,-78.4,-38.2C-70.7,-53,-60.2,-66.3,-46.6,-72.1C-33,-78,-16.5,-76.4,-2.3,-72.3C11.9,-68.2,23.9,-61.5,37.5,-65.1Z"
                transform="translate(100 100)"
              />
            </svg>
          </div>

          <div
            class="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
          >
            <div>
              <span
                class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-white uppercase rounded-full bg-white/20 backdrop-blur-sm mb-4"
              >
                Featured Service
              </span>
              <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Complete Digital Transformation
              </h2>
              <p class="text-white/90 text-lg mb-8">
                We provide end-to-end digital transformation services to
                modernize your business operations, enhance customer
                experiences, and drive growth through innovative technology
                solutions.
              </p>

              <div class="space-y-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0 w-5 h-5 mt-1">
                    <svg
                      class="w-5 h-5 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <p class="ml-3 text-white/90">
                    Strategic digital roadmap development
                  </p>
                </div>
                <div class="flex items-start">
                  <div class="flex-shrink-0 w-5 h-5 mt-1">
                    <svg
                      class="w-5 h-5 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <p class="ml-3 text-white/90">Legacy system modernization</p>
                </div>
                <div class="flex items-start">
                  <div class="flex-shrink-0 w-5 h-5 mt-1">
                    <svg
                      class="w-5 h-5 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <p class="ml-3 text-white/90">
                    Cloud migration and infrastructure optimization
                  </p>
                </div>
                <div class="flex items-start">
                  <div class="flex-shrink-0 w-5 h-5 mt-1">
                    <svg
                      class="w-5 h-5 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <p class="ml-3 text-white/90">Digital process automation</p>
                </div>
              </div>

              <div class="mt-8">
                <a
                  href="services/digital-transformation.html"
                  class="inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-full text-primary-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                >
                  <span>Explore Digital Transformation</span>
                  <svg
                    class="ml-2 -mr-1 w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </a>
              </div>
            </div>

            <div class="relative">
              <div
                class="relative rounded-xl overflow-hidden shadow-2xl animate-float"
              >
                <img
                  src="https://www.pngplay.com/wp-content/uploads/7/Online-Digital-Marketing-PNG-Clipart-Background.png"
                  alt="Digital Transformation"
                  class="w-full h-auto object-cover"
                />

                <!-- Overlay Stats -->
                <div
                  class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6"
                >
                  <div class="grid grid-cols-2 gap-4">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                      <p class="text-white/80 text-sm font-medium">
                        Average ROI
                      </p>
                      <p class="text-white text-2xl font-bold">289%</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                      <p class="text-white/80 text-sm font-medium">
                        Efficiency Gained
                      </p>
                      <p class="text-white text-2xl font-bold">47%</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Floating Badge -->
              <div
                class="absolute -top-6 -right-6 bg-white rounded-full shadow-lg p-4 animate-bounce-slow"
              >
                <div class="flex items-center space-x-1">
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <span class="text-gray-900 font-bold">Most Popular</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Digital Media Management Section -->
    <section
      id="digital-media-management"
      class="py-24 relative overflow-hidden"
    >
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blobs -->
        <div
          class="absolute top-40 right-20 w-96 h-96 bg-gradient-to-br from-purple-300/30 to-purple-600/30 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"
        ></div>
        <div
          class="absolute -bottom-40 -left-20 w-96 h-96 bg-gradient-to-tr from-pink-300/30 to-purple-600/30 rounded-full filter blur-3xl opacity-30 animate-float"
          style="animation-delay: 1.5s"
        ></div>

        <!-- Decorative Circle Grid Pattern -->
        <div class="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <pattern
              id="social-media-dots"
              x="0"
              y="0"
              width="30"
              height="30"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <circle
                id="social-media-dot"
                cx="15"
                cy="15"
                r="1.5"
                fill="#6d28d9"
              ></circle>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#social-media-dots)"
            ></rect>
          </svg>
        </div>

        <!-- Decorative Swirl SVG -->
        <svg
          class="absolute top-0 right-0 w-1/3 h-auto text-purple-100 opacity-20"
          viewBox="0 0 200 200"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill="currentColor"
            d="M44.3,-76.5C58.4,-71.2,71.3,-61.3,79.4,-48.1C87.5,-34.8,90.8,-18.4,90.3,-2.4C89.8,13.5,85.6,27.1,77.2,38.2C68.9,49.3,56.3,58,43.1,65.5C29.9,73,14.9,79.4,0.3,78.9C-14.3,78.5,-28.7,71.3,-42.7,63.3C-56.7,55.3,-70.4,46.4,-78.8,33.5C-87.2,20.6,-90.3,3.6,-87.4,-12.1C-84.5,-27.8,-75.5,-42.2,-63.9,-53.4C-52.3,-64.5,-38.1,-72.3,-23.6,-76.9C-9.1,-81.5,5.7,-82.9,19.4,-80.8C33.1,-78.7,45.7,-73.2,44.3,-76.5Z"
            transform="translate(100 100)"
          />
        </svg>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-16" data-aos="fade-up">
          <span
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-purple-700 uppercase rounded-full bg-purple-100 mb-4"
          >
            Digital Presence Management
          </span>
          <h2 class="text-3xl md:text-5xl font-extrabold text-gray-900 mb-4">
            Elevate Your
            <span
              class="text-gradient bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"
              >Social Media</span
            >
            Presence
          </h2>
          <p class="text-xl text-gray-600">
            We design, create, and manage your entire social media ecosystem to
            boost engagement, increase brand awareness, and drive conversions.
          </p>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          <!-- Left Column: Image Collage -->
          <div class="relative" data-aos="fade-right" data-aos-delay="100">
            <div class="relative grid grid-cols-2 gap-4">
              <!-- Main Large Image -->
              <div class="col-span-2 overflow-hidden rounded-2xl shadow-xl">
                <img
                  src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?auto=format&fit=crop&w=800&q=80"
                  alt="Social Media Management"
                  class="w-full h-auto object-cover transform hover:scale-105 transition-transform duration-700"
                />
              </div>

              <!-- Instagram Post Example -->
              <div class="overflow-hidden rounded-2xl shadow-xl">
                <img
                  src="https://images.unsplash.com/photo-1563986768494-4dee2763ff3f?auto=format&fit=crop&w=400&q=80"
                  alt="Instagram Post"
                  class="w-full h-48 object-cover transform hover:scale-105 transition-transform duration-700"
                />
              </div>

              <!-- LinkedIn Post Example -->
              <div class="overflow-hidden rounded-2xl shadow-xl">
                <img
                  src="https://images.unsplash.com/photo-1560472355-536de3962603?auto=format&fit=crop&w=400&q=80"
                  alt="LinkedIn Post"
                  class="w-full h-48 object-cover transform hover:scale-105 transition-transform duration-700"
                />
              </div>

              <!-- Floating Social Media Icons -->
              <div
                class="absolute -top-6 -right-6 bg-white rounded-full shadow-lg p-4 animate-float"
              >
                <div class="flex items-center space-x-2">
                  <i class="fab fa-instagram text-lg text-pink-600"></i>
                  <i class="fab fa-facebook text-lg text-blue-600"></i>
                  <i class="fab fa-linkedin text-lg text-blue-700"></i>
                </div>
              </div>

              <!-- Floating Stats Badge -->
              <div
                class="absolute -bottom-6 -left-6 bg-white rounded-xl shadow-lg p-3 animate-float"
                style="animation-delay: 1s"
              >
                <div class="flex items-center space-x-2">
                  <div
                    class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center"
                  >
                    <i class="fas fa-chart-line text-purple-600"></i>
                  </div>
                  <div>
                    <p class="text-xs text-gray-500">Avg. Engagement</p>
                    <p class="text-sm font-bold text-gray-800">+278%</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column: Content -->
          <div data-aos="fade-left" data-aos-delay="200">
            <h3 class="text-2xl font-bold text-gray-900 mb-6">
              Comprehensive Social Media Management Services
            </h3>
            <p class="text-gray-600 mb-8">
              We handle every aspect of your social media presence, from content
              creation to community management, allowing you to focus on your
              core business while we build your digital brand presence.
            </p>

            <!-- Service Cards -->
            <div class="space-y-6">
              <!-- Content Creation Card -->
              <div
                class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-all duration-300 border border-gray-100 transform hover:-translate-y-1"
              >
                <div class="flex items-start">
                  <div
                    class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4 flex-shrink-0"
                  >
                    <i class="fas fa-palette text-purple-600 text-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">
                      Creative Content Design
                    </h4>
                    <p class="text-gray-600">
                      Eye-catching posts, stories, reels, and videos designed to
                      captivate your audience and reflect your brand identity.
                    </p>
                    <div class="mt-3 flex space-x-3">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                        >Posts</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800"
                        >Stories</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >Reels</span
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- Engagement Card -->
              <div
                class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-all duration-300 border border-gray-100 transform hover:-translate-y-1"
              >
                <div class="flex items-start">
                  <div
                    class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4 flex-shrink-0"
                  >
                    <i class="fas fa-comments text-purple-600 text-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">
                      Community Management
                    </h4>
                    <p class="text-gray-600">
                      Timely responses to comments, messages, and mentions. We
                      build relationships with your audience to foster loyalty
                      and trust.
                    </p>
                    <div class="mt-3 flex space-x-3">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                        >DM Replies</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                        >Comments</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                        >Mentions</span
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- Strategy Card -->
              <div
                class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-all duration-300 border border-gray-100 transform hover:-translate-y-1"
              >
                <div class="flex items-start">
                  <div
                    class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4 flex-shrink-0"
                  >
                    <i class="fas fa-bullseye text-purple-600 text-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">
                      Growth Strategy & Paid Promotion
                    </h4>
                    <p class="text-gray-600">
                      Strategic paid campaigns, influencer collaborations, and
                      engagement tactics designed to expand your reach and grow
                      your following.
                    </p>
                    <div class="mt-3 flex space-x-3">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                        >Ad Campaigns</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >Influencers</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                        >Analytics</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- CTA Button -->
            <div class="mt-8">
              <a
                href="#social-media-contact"
                class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                <span>Boost Your Social Presence Today</span>
                <svg
                  class="ml-2 -mr-1 w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </a>
            </div>
          </div>
        </div>

        <!-- Platform-Specific Features -->
        <div class="mb-20" data-aos="fade-up" data-aos-delay="300">
          <h3 class="text-2xl font-bold text-gray-900 text-center mb-12">
            Platform-Specific Excellence
          </h3>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Instagram Card -->
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 group"
            >
              <div class="relative h-60 overflow-hidden">
                <img
                  src="https://static1.howtogeekimages.com/wordpress/wp-content/uploads/2021/08/instagram_hero_1200_675.png"
                  alt="Instagram Management"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-pink-600/40 opacity-80"
                ></div>
                <div class="absolute inset-0 flex flex-col justify-end p-6">
                  <div class="flex items-center mb-4">
                    <i class="fab fa-instagram text-2xl text-white mr-3"></i>
                    <h4 class="text-xl font-bold text-white">Instagram</h4>
                  </div>
                  <p class="text-white/90">
                    Visually stunning content that captures attention in crowded
                    feeds and drives engagement.
                  </p>
                </div>
              </div>

              <div class="p-6">
                <ul class="space-y-3">
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-purple-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Scroll-stopping feed posts & carousels</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-purple-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Engaging stories & interactive polls</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-purple-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Trend-aware Reels & video content</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-purple-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Hashtag strategy & audience targeting</span
                    >
                  </li>
                </ul>

                <div class="mt-6 pt-6 border-t border-gray-100">
                  <div class="flex justify-between items-center">
                    <div>
                      <span class="text-sm text-gray-500"
                        >Average Engagement Rate</span
                      >
                      <p class="text-lg font-bold text-purple-600">4.7%</p>
                    </div>
                    <a
                      href="#instagram-services"
                      class="inline-flex items-center text-purple-600 hover:text-purple-800 font-medium transition-colors duration-300"
                    >
                      Learn More
                      <svg
                        class="ml-2 w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M14 5l7 7m0 0l-7 7m7-7H3"
                        ></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- LinkedIn Card -->
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 group"
            >
              <div class="relative h-60 overflow-hidden">
                <img
                  src="https://einsteinmarketer.ams3.digitaloceanspaces.com/em/uploads/2020/03/linkedin-career-advice-600x300-1.png"
                  alt="LinkedIn Management"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-blue-900/80 to-blue-600/40 opacity-80"
                ></div>
                <div class="absolute inset-0 flex flex-col justify-end p-6">
                  <div class="flex items-center mb-4">
                    <i class="fab fa-linkedin-in text-2xl text-white mr-3"></i>
                    <h4 class="text-xl font-bold text-white">LinkedIn</h4>
                  </div>
                  <p class="text-white/90">
                    Professional content that establishes thought leadership and
                    drives B2B connections.
                  </p>
                </div>
              </div>

              <div class="p-6">
                <ul class="space-y-3">
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-blue-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Thought leadership articles & posts</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-blue-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Company page optimization & management</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-blue-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >B2B lead generation campaigns</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-blue-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Industry-specific content strategies</span
                    >
                  </li>
                </ul>

                <div class="mt-6 pt-6 border-t border-gray-100">
                  <div class="flex justify-between items-center">
                    <div>
                      <span class="text-sm text-gray-500"
                        >Connection Growth Rate</span
                      >
                      <p class="text-lg font-bold text-blue-600">32%</p>
                    </div>
                    <a
                      href="#linkedin-services"
                      class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-300"
                    >
                      Learn More
                      <svg
                        class="ml-2 w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M14 5l7 7m0 0l-7 7m7-7H3"
                        ></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Facebook Card -->
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 group"
            >
              <div class="relative h-60 overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1611162618071-b39a2ec055fb?auto=format&fit=crop&w=600&q=80"
                  alt="Facebook Management"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-blue-900/80 to-indigo-600/40 opacity-80"
                ></div>
                <div class="absolute inset-0 flex flex-col justify-end p-6">
                  <div class="flex items-center mb-4">
                    <i class="fab fa-facebook-f text-2xl text-white mr-3"></i>
                    <h4 class="text-xl font-bold text-white">Facebook</h4>
                  </div>
                  <p class="text-white/90">
                    Community-building content that engages audiences and drives
                    meaningful interactions.
                  </p>
                </div>
              </div>

              <div class="p-6">
                <ul class="space-y-3">
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-indigo-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Engaging page content & community posts</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-indigo-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Group management & community building</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-indigo-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Targeted ad campaigns & retargeting</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-indigo-600 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-gray-600"
                      >Interactive live videos & events</span
                    >
                  </li>
                </ul>

                <div class="mt-6 pt-6 border-t border-gray-100">
                  <div class="flex justify-between items-center">
                    <div>
                      <span class="text-sm text-gray-500"
                        >Audience Growth Rate</span
                      >
                      <p class="text-lg font-bold text-indigo-600">27%</p>
                    </div>
                    <a
                      href="#facebook-services"
                      class="inline-flex items-center text-indigo-600 hover:text-indigo-800 font-medium transition-colors duration-300"
                    >
                      Learn More
                      <svg
                        class="ml-2 w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M14 5l7 7m0 0l-7 7m7-7H3"
                        ></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Success Stories Slider -->
        <div class="mb-20" data-aos="fade-up" data-aos-delay="400">
          <h3 class="text-2xl font-bold text-gray-900 text-center mb-4">
            Success Stories
          </h3>
          <p class="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            See how we've transformed the social media presence of businesses
            just like yours, driving real business results through strategic
            digital engagement.
          </p>

          <div class="relative overflow-hidden">
            <!-- Success Stories Slider -->
            <div
              class="social-media-success-slider flex transition-transform duration-500"
              id="socialMediaSuccessSlider"
            >
              <!-- Success Story 1 -->
              <div class="success-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft overflow-hidden h-full border border-gray-100"
                >
                  <div class="relative h-48 overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1556742502-ec7c0e9f34b1?auto=format&fit=crop&w=600&q=80"
                      alt="Fashion Brand Case Study"
                      class="w-full h-full object-cover"
                    />
                    <div
                      class="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"
                    ></div>
                    <div class="absolute bottom-4 left-4">
                      <span
                        class="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-purple-700"
                        >Fashion Brand</span
                      >
                    </div>
                  </div>

                  <div class="p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-3">
                      LuxeStyle Boutique
                    </h4>
                    <p class="text-gray-600 mb-4">
                      Increased Instagram engagement by 215% and drove a 43%
                      increase in website traffic through strategic content and
                      influencer partnerships.
                    </p>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Followers Growth</p>
                        <p class="text-xl font-bold text-purple-600">+187%</p>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Sales Conversion</p>
                        <p class="text-xl font-bold text-purple-600">+43%</p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="flex space-x-1">
                        <i class="fab fa-instagram text-pink-600"></i>
                        <i class="fab fa-facebook text-blue-600"></i>
                      </div>
                      <a
                        href="#case-studies"
                        class="inline-flex items-center text-purple-600 hover:text-purple-800 font-medium transition-colors duration-300 text-sm"
                      >
                        Read Case Study
                        <svg
                          class="ml-2 w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          ></path>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Success Story 2 -->
              <div class="success-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft overflow-hidden h-full border border-gray-100"
                >
                  <div class="relative h-48 overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=600&q=80"
                      alt="Financial Services Case Study"
                      class="w-full h-full object-cover"
                    />
                    <div
                      class="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"
                    ></div>
                    <div class="absolute bottom-4 left-4">
                      <span
                        class="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-blue-700"
                        >Financial Services</span
                      >
                    </div>
                  </div>

                  <div class="p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-3">
                      Apex Financial Advisors
                    </h4>
                    <p class="text-gray-600 mb-4">
                      Established thought leadership on LinkedIn generating 120+
                      qualified B2B leads per month through strategic content
                      marketing.
                    </p>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Lead Generation</p>
                        <p class="text-xl font-bold text-blue-600">+320%</p>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Content Reach</p>
                        <p class="text-xl font-bold text-blue-600">+178%</p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="flex space-x-1">
                        <i class="fab fa-linkedin text-blue-700"></i>
                      </div>
                      <a
                        href="#case-studies"
                        class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-300 text-sm"
                      >
                        Read Case Study
                        <svg
                          class="ml-2 w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          ></path>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Success Story 3 -->
              <div class="success-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft overflow-hidden h-full border border-gray-100"
                >
                  <div class="relative h-48 overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1505373877841-8d25f7d46678?auto=format&fit=crop&w=600&q=80"
                      alt="Food & Beverage Case Study"
                      class="w-full h-full object-cover"
                    />
                    <div
                      class="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"
                    ></div>
                    <div class="absolute bottom-4 left-4">
                      <span
                        class="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-green-700"
                        >Food & Beverage</span
                      >
                    </div>
                  </div>

                  <div class="p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-3">
                      Urban Coffee Roasters
                    </h4>
                    <p class="text-gray-600 mb-4">
                      Created a vibrant social community with user-generated
                      content driving 68% increase in store visits and online
                      orders.
                    </p>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Social Engagement</p>
                        <p class="text-xl font-bold text-green-600">+256%</p>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Revenue Growth</p>
                        <p class="text-xl font-bold text-green-600">+68%</p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="flex space-x-1">
                        <i class="fab fa-instagram text-pink-600"></i>
                        <i class="fab fa-facebook text-blue-600"></i>
                        <i class="fab fa-tiktok text-black"></i>
                      </div>
                      <a
                        href="#case-studies"
                        class="inline-flex items-center text-green-600 hover:text-green-800 font-medium transition-colors duration-300 text-sm"
                      >
                        Read Case Study
                        <svg
                          class="ml-2 w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          ></path>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Success Story 4 -->
              <div class="success-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft overflow-hidden h-full border border-gray-100"
                >
                  <div class="relative h-48 overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=600&q=80"
                      alt="Tech Startup Case Study"
                      class="w-full h-full object-cover"
                    />
                    <div
                      class="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"
                    ></div>
                    <div class="absolute bottom-4 left-4">
                      <span
                        class="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-indigo-700"
                        >Tech Startup</span
                      >
                    </div>
                  </div>

                  <div class="p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-3">
                      NexaCloud Solutions
                    </h4>
                    <p class="text-gray-600 mb-4">
                      Comprehensive social strategy that positioned the brand as
                      an industry leader, resulting in 3 major partnership
                      deals.
                    </p>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Brand Mentions</p>
                        <p class="text-xl font-bold text-indigo-600">+412%</p>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Demo Requests</p>
                        <p class="text-xl font-bold text-indigo-600">+94%</p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="flex space-x-1">
                        <i class="fab fa-linkedin text-blue-700"></i>
                        <i class="fab fa-twitter text-blue-400"></i>
                      </div>
                      <a
                        href="#case-studies"
                        class="inline-flex items-center text-indigo-600 hover:text-indigo-800 font-medium transition-colors duration-300 text-sm"
                      >
                        Read Case Study
                        <svg
                          class="ml-2 w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          ></path>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Slider Controls -->
            <div class="flex justify-center items-center mt-8">
              <button
                id="prevSuccessStory"
                class="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-purple-50 hover:text-purple-600 transition-colors duration-300 mr-4 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  ></path>
                </svg>
              </button>

              <div class="flex space-x-2">
                <button
                  class="success-dot w-3 h-3 rounded-full bg-purple-600"
                ></button>
                <button
                  class="success-dot w-3 h-3 rounded-full bg-gray-300"
                ></button>
                <button
                  class="success-dot w-3 h-3 rounded-full bg-gray-300"
                ></button>
                <button
                  class="success-dot w-3 h-3 rounded-full bg-gray-300"
                ></button>
              </div>

              <button
                id="nextSuccessStory"
                class="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-purple-50 hover:text-purple-600 transition-colors duration-300 ml-4 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Call to Action Banner -->
        <div
          class="relative rounded-3xl overflow-hidden"
          data-aos="fade-up"
          data-aos-delay="500"
        >
          <div class="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1557804506-669a67965ba0?auto=format&fit=crop&w=1200&q=80"
              alt="Social Media Management"
              class="w-full h-full object-cover"
            />
            <div
              class="absolute inset-0 bg-gradient-to-r from-purple-900/90 to-pink-900/80"
            ></div>
          </div>

          <div class="relative p-8 md:p-12 lg:p-16">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h2 class="text-3xl md:text-4xl font-extrabold text-white mb-6">
                  Ready to Transform Your Social Media Presence?
                </h2>
                <p class="text-white/90 text-xl mb-8">
                  Partner with our expert team to create engaging content, build
                  vibrant communities, and drive real business results through
                  strategic social media management.
                </p>

                <ul class="space-y-4 mb-8">
                  <li class="flex items-start">
                    <svg
                      class="w-6 h-6 text-pink-400 mr-3 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-white"
                      >Dedicated account manager & content team</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-6 h-6 text-pink-400 mr-3 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-white"
                      >Monthly content calendars & performance reports</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-6 h-6 text-pink-400 mr-3 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-white"
                      >Rapid response community management</span
                    >
                  </li>
                </ul>

                <a
                  href="#social-media-contact"
                  id="socialMediaCTA"
                  class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-purple-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                >
                  <span>Get Your Free Social Media Audit</span>
                  <svg
                    class="ml-2 -mr-1 w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </a>
              </div>

              <!-- Contact Form -->
              <div id="social-media-contact">
                <div
                  class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20"
                >
                  <h3 class="text-xl font-bold text-white mb-4">
                    Request Your Free Social Media Audit
                  </h3>
                  <p class="text-white/80 mb-6">
                    Fill out the form below and our team will analyze your
                    current social media presence and provide actionable
                    recommendations.
                  </p>

                  <form
                    action="https://submit-form.com/l2ArscGWs"
                    class="space-y-4"
                  >
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label for="social-name" class="sr-only"
                          >Your Name</label
                        >
                        <input
                          type="text"
                          id="social-name"
                          name="social-name"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                          placeholder="Your Name"
                        />
                      </div>
                      <div>
                        <label for="social-email" class="sr-only"
                          >Your Email</label
                        >
                        <input
                          type="email"
                          id="social-email"
                          name="social-email"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                          placeholder="Your Email"
                        />
                      </div>
                    </div>

                    <div>
                      <label for="social-company" class="sr-only"
                        >Company Name</label
                      >
                      <input
                        type="text"
                        id="social-company"
                        name="social-company"
                        class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                        placeholder="Company Name"
                      />
                    </div>

                    <div>
                      <label for="social-platforms" class="sr-only"
                        >Social Media Platforms</label
                      >
                      <select
                        id="social-platforms"
                        name="social-platforms"
                        class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                      >
                        <option
                          value=""
                          disabled
                          selected
                          class="text-gray-500"
                        >
                          Primary Platform of Interest
                        </option>
                        <option value="instagram" class="text-gray-800">
                          Instagram
                        </option>
                        <option value="facebook" class="text-gray-800">
                          Facebook
                        </option>
                        <option value="linkedin" class="text-gray-800">
                          LinkedIn
                        </option>
                        <option value="twitter" class="text-gray-800">
                          Twitter
                        </option>
                        <option value="tiktok" class="text-gray-800">
                          TikTok
                        </option>
                        <option value="multiple" class="text-gray-800">
                          Multiple Platforms
                        </option>
                      </select>
                    </div>

                    <div>
                      <label for="social-message" class="sr-only"
                        >Tell us about your goals</label
                      >
                      <textarea
                        id="social-message"
                        name="social-message"
                        rows="3"
                        class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                        placeholder="Tell us about your social media goals..."
                      ></textarea>
                    </div>

                    <button
                      type="submit"
                      class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium rounded-lg text-purple-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                    >
                      Request Free Audit
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- JavaScript for Social Media Management Section -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Success Stories Slider
        const successSlider = document.getElementById(
          "socialMediaSuccessSlider"
        );
        const successSlides = document.querySelectorAll(".success-slide");
        const successDots = document.querySelectorAll(".success-dot");
        const prevSuccessBtn = document.getElementById("prevSuccessStory");
        const nextSuccessBtn = document.getElementById("nextSuccessStory");
        let successCurrentIndex = 0;
        const successSlidesToShow = window.innerWidth >= 768 ? 3 : 1;
        const successSlideWidth = 100 / successSlidesToShow;

        // Initialize slider
        function initSuccessSlider() {
          successSlides.forEach((slide) => {
            slide.style.minWidth = `${successSlideWidth}%`;
          });
          goToSuccessSlide(0);
        }

        // Go to specific slide
        function goToSuccessSlide(index) {
          if (index < 0) {
            index = successSlides.length - successSlidesToShow;
          } else if (index > successSlides.length - successSlidesToShow) {
            index = 0;
          }

          successCurrentIndex = index;
          const translateValue = -index * successSlideWidth;
          successSlider.style.transform = `translateX(${translateValue}%)`;

          // Update dots
          successDots.forEach((dot, i) => {
            dot.classList.remove("bg-purple-600");
            dot.classList.add("bg-gray-300");

            if (i === Math.floor(index / successSlidesToShow)) {
              dot.classList.remove("bg-gray-300");
              dot.classList.add("bg-purple-600");
            }
          });
        }

        // Event listeners
        prevSuccessBtn.addEventListener("click", () => {
          goToSuccessSlide(successCurrentIndex - 1);
        });

        nextSuccessBtn.addEventListener("click", () => {
          goToSuccessSlide(successCurrentIndex + 1);
        });

        successDots.forEach((dot, i) => {
          dot.addEventListener("click", () => {
            goToSuccessSlide(i * successSlidesToShow);
          });
        });

        // Responsive adjustment
        window.addEventListener("resize", initSuccessSlider);

        // Initialize on load
        initSuccessSlider();

        // Auto slide
        setInterval(() => {
          goToSuccessSlide(successCurrentIndex + 1);
        }, 5000);

        // Smooth scroll for CTA button
        document
          .getElementById("socialMediaCTA")
          .addEventListener("click", function (e) {
            e.preventDefault();

            const targetId = this.getAttribute("href").substring(1);
            const targetElement = document.getElementById(targetId);

            window.scrollTo({
              top: targetElement.offsetTop - 100,
              behavior: "smooth",
            });
          });
      });
    </script>
    <!-- About Us Section with Company Story -->
    <section id="about-us" class="py-20 bg-white relative overflow-hidden">
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blob -->
        <div
          class="absolute -top-32 -right-32 w-96 h-96 bg-gradient-to-br from-primary-100 to-primary-300 rounded-full filter blur-3xl opacity-30 animate-float"
        ></div>
        <div
          class="absolute -bottom-32 -left-32 w-96 h-96 bg-gradient-to-tr from-secondary-100 to-secondary-300 rounded-full filter blur-3xl opacity-30 animate-float"
          style="animation-delay: 2s"
        ></div>

        <!-- SVG Pattern Background -->
        <div class="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <pattern
              id="pattern-circles"
              x="0"
              y="0"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <circle
                id="pattern-circle"
                cx="20"
                cy="20"
                r="3.5"
                fill="#6d28d9"
              ></circle>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#pattern-circles)"
            ></rect>
          </svg>
        </div>

        <!-- SVG Wave Shape -->
        <svg
          class="absolute top-0 left-0 w-full"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
        >
          <path
            fill="rgba(237, 233, 254, 0.5)"
            fill-opacity="1"
            d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,85.3C672,75,768,85,864,112C960,139,1056,181,1152,181.3C1248,181,1344,139,1392,117.3L1440,96L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z"
          ></path>
        </svg>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- Left Column: Company Story -->
          <div
            class="lg:pr-10"
            data-aos="fade-right"
            data-aos-delay="100"
            data-aos-duration="1000"
          >
            <span
              class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
            >
              Our Story
            </span>

            <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-6">
              Not Just a Digital Agency—A
              <span class="text-gradient">Future-Forging</span> Innovation
              Powerhouse
            </h2>

            <p class="text-lg text-gray-600 mb-6 leading-relaxed">
              At RayDesign Technologies, we are a strategic transformation
              partner that merges the best of design thinking, intelligent
              automation, full-stack engineering, and marketing science to
              architect high-impact, human-centered digital ecosystems.
            </p>

            <p class="text-lg text-gray-600 mb-8 leading-relaxed">
              Our ethos is rooted in transforming complex, chaotic business
              challenges into seamless, scalable, and emotionally engaging
              digital experiences—fueling business growth, operational
              efficiency, and brand transcendence in a fast-paced, hyper-digital
              world.
            </p>

            <!-- Company Values -->
            <div class="space-y-6 mb-8">
              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div
                    class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-primary-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M5.05 3.636a1 1 0 010 1.414 7 7 0 000 9.9 1 1 0 11-1.414 1.414 9 9 0 010-12.728 1 1 0 011.414 0zm9.9 0a1 1 0 011.414 0 9 9 0 010 12.728 1 1 0 11-1.414-1.414 7 7 0 000-9.9 1 1 0 010-1.414zM7.879 6.464a1 1 0 010 1.414 3 3 0 000 4.243 1 1 0 11-1.415 1.414 5 5 0 010-7.07 1 1 0 011.415 0zm4.242 0a1 1 0 011.415 0 5 5 0 010 7.072 1 1 0 01-1.415-1.415 3 3 0 000-4.242 1 1 0 010-1.415z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900">
                    Innovation at Core
                  </h3>
                  <p class="text-gray-600">
                    We constantly push boundaries, embracing emerging
                    technologies and methodologies to deliver cutting-edge
                    solutions.
                  </p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div
                    class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-primary-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900">
                    Human-Centered Approach
                  </h3>
                  <p class="text-gray-600">
                    We design with empathy, putting users at the center of
                    everything we create to build meaningful digital
                    experiences.
                  </p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div
                    class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-primary-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
                      ></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900">
                    Collaborative Excellence
                  </h3>
                  <p class="text-gray-600">
                    We work closely with our clients as true partners, combining
                    our expertise with their industry knowledge.
                  </p>
                </div>
              </div>
            </div>

            <!-- CTA Button -->
            <a
              href="#contact"
              class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span>Partner With Us</span>
              <svg
                class="ml-2 -mr-1 w-5 h-5"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </a>
          </div>

          <!-- Right Column: Image Collage -->
          <div
            class="lg:pl-10 relative"
            data-aos="fade-left"
            data-aos-delay="300"
            data-aos-duration="1000"
          >
            <div class="relative h-[500px] w-full">
              <!-- Main Image -->
              <div
                class="absolute top-0 right-0 w-[75%] h-[65%] rounded-2xl overflow-hidden shadow-xl z-20 animate-float"
                style="animation-delay: 0.5s"
              >
                <img
                  src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?auto=format&fit=crop&w=800&q=80"
                  alt="RayDesign Team Collaboration"
                  class="w-full h-full object-cover"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-tr from-primary-900/40 to-transparent"
                ></div>
              </div>

              <!-- Secondary Image -->
              <div
                class="absolute bottom-0 left-0 w-[65%] h-[55%] rounded-2xl overflow-hidden shadow-xl z-10 animate-float"
                style="animation-delay: 1s"
              >
                <img
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?auto=format&fit=crop&w=800&q=80"
                  alt="RayDesign Office Culture"
                  class="w-full h-full object-cover"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-tr from-primary-900/40 to-transparent"
                ></div>
              </div>

              <!-- Decorative Elements -->
              <div
                class="absolute top-[40%] left-[20%] w-16 h-16 rounded-full bg-primary-100 z-30 animate-pulse flex items-center justify-center"
              >
                <svg
                  class="w-8 h-8 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>

              <!-- Stats Card -->
              <div
                class="absolute -bottom-6 right-10 bg-white rounded-xl shadow-lg p-4 z-30"
              >
                <div class="grid grid-cols-2 gap-6">
                  <div class="text-center">
                    <h4 class="text-3xl font-bold text-primary-600">8+</h4>
                    <p class="text-gray-600 text-sm">Years Experience</p>
                  </div>
                  <div class="text-center">
                    <h4 class="text-3xl font-bold text-primary-600">500+</h4>
                    <p class="text-gray-600 text-sm">Happy Clients</p>
                  </div>
                </div>
              </div>

              <!-- Pattern Background -->
              <div
                class="absolute -bottom-4 -right-4 w-32 h-32 bg-dots opacity-20 z-0"
              ></div>
            </div>
          </div>
        </div>

        <!-- Mission & Vision Section -->
        <div class="mt-24 grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Mission Card -->
          <div
            class="bg-white rounded-2xl p-8 shadow-soft border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <div class="flex items-center mb-6">
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mr-4"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-gray-900">Our Mission</h3>
            </div>
            <p class="text-gray-600 leading-relaxed mb-6">
              To empower businesses through innovative digital solutions that
              solve complex challenges, enhance user experiences, and drive
              sustainable growth. We are committed to delivering excellence
              through a perfect blend of creativity, technology, and strategic
              thinking.
            </p>
            <div class="pt-4 border-t border-gray-100">
              <div class="flex items-center">
                <img
                  src="https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?auto=format&fit=crop&w=100&h=100&q=80"
                  alt="CEO"
                  class="w-10 h-10 rounded-full object-cover mr-3"
                />
              </div>
            </div>
          </div>

          <!-- Vision Card -->
          <div
            class="bg-white rounded-2xl p-8 shadow-soft border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <div class="flex items-center mb-6">
              <div
                class="w-12 h-12 rounded-full bg-secondary-100 flex items-center justify-center mr-4"
              >
                <svg
                  class="w-6 h-6 text-secondary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-gray-900">Our Vision</h3>
            </div>
            <p class="text-gray-600 leading-relaxed mb-6">
              To be the global leader in digital innovation, recognized for
              transforming businesses through cutting-edge technology solutions.
              We envision a world where every business, regardless of size, has
              access to the tools and expertise needed to thrive in the digital
              landscape.
            </p>
            <div class="pt-4 border-t border-gray-100">
              <div class="flex items-center">
                <img
                  src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?auto=format&fit=crop&w=100&h=100&q=80"
                  alt="CTO"
                  class="w-10 h-10 rounded-full object-cover mr-3"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Process Section -->
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 pt-24 relative z-10">
        <div class="text-center max-w-3xl mx-auto mb-16" data-aos="fade-up">
          <span
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
          >
            Our Process
          </span>
          <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">
            Our Process of <span class="text-gradient">Excellence</span>
          </h2>
          <p class="text-xl text-gray-600">
            At RayDesign Technologies, we follow a multi-disciplinary and
            precision-engineered process that ensures every digital solution is
            not only beautiful and intelligent—but strategically aligned with
            measurable business outcomes.
          </p>
        </div>

        <!-- Process Timeline -->
        <div class="relative">
          <!-- Vertical Line -->
          <div
            class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-primary-400 to-primary-600 rounded-full hidden md:block"
          ></div>

          <div class="space-y-12">
            <!-- Process Step 1 -->
            <div class="relative" data-aos="fade-up" data-aos-delay="100">
              <div
                class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"
              ></div>

              <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                  >
                    <h3
                      class="text-xl font-bold text-gray-900 mb-3 flex items-center justify-end"
                    >
                      <span>Discovery & Strategic Alignment</span>
                      <div
                        class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center ml-4 flex-shrink-0"
                      >
                        <span class="text-primary-600 font-bold">1</span>
                      </div>
                    </h3>
                    <p class="text-gray-600 mb-4">
                      We begin with a comprehensive discovery phase to
                      understand your business objectives, target audience, and
                      competitive landscape.
                    </p>
                    <ul class="space-y-2 text-right">
                      <li class="flex items-center justify-end">
                        <span>Deep-dive brand immersion workshops</span>
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                      <li class="flex items-center justify-end">
                        <span
                          >Industry benchmarking and competitor gap
                          analysis</span
                        >
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                      <li class="flex items-center justify-end">
                        <span>Stakeholder interviews and KPI alignment</span>
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="md:w-1/2 md:pl-12">
                  <div class="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src="https://images.unsplash.com/photo-1552664730-d307ca884978?auto=format&fit=crop&w=800&q=80"
                      alt="Discovery Phase"
                      class="w-full h-64 object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Step 2 -->
            <div class="relative" data-aos="fade-up" data-aos-delay="200">
              <div
                class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"
              ></div>

              <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0 md:order-2">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                  >
                    <h3
                      class="text-xl font-bold text-gray-900 mb-3 flex items-center"
                    >
                      <div
                        class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-4 flex-shrink-0"
                      >
                        <span class="text-primary-600 font-bold">2</span>
                      </div>
                      <span>Ideation & Prototyping</span>
                    </h3>
                    <p class="text-gray-600 mb-4">
                      We transform insights into actionable concepts, rapidly
                      prototyping solutions to validate ideas before full
                      development.
                    </p>
                    <ul class="space-y-2">
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Design sprints to rapidly visualize core
                          functionalities</span
                        >
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Wireframing, low-fidelity UX mapping, and journey
                          visualization</span
                        >
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Stakeholder validation loops for early feedback</span
                        >
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="md:w-1/2 md:pl-12 md:order-1">
                  <div class="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src="https://images.unsplash.com/photo-1542744173-05336fcc7ad4?auto=format&fit=crop&w=800&q=80"
                      alt="Ideation Phase"
                      class="w-full h-64 object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Step 3 -->
            <div class="relative" data-aos="fade-up" data-aos-delay="300">
              <div
                class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"
              ></div>

              <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                  >
                    <h3
                      class="text-xl font-bold text-gray-900 mb-3 flex items-center justify-end"
                    >
                      <span>Engineering & Implementation</span>
                      <div
                        class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center ml-4 flex-shrink-0"
                      >
                        <span class="text-primary-600 font-bold">3</span>
                      </div>
                    </h3>
                    <p class="text-gray-600 mb-4">
                      Our engineering team brings concepts to life using the
                      latest technologies and best practices for robust,
                      scalable solutions.
                    </p>
                    <ul class="space-y-2 text-right">
                      <li class="flex items-center justify-end">
                        <span>Agile sprint cycles with CI/CD pipelines</span>
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                      <li class="flex items-center justify-end">
                        <span
                          >Secure codebases, microservices architecture, and
                          API-first development</span
                        >
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                      <li class="flex items-center justify-end">
                        <span
                          >QA automation, manual testing, and continuous
                          optimization</span
                        >
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="md:w-1/2 md:pl-12">
                  <div class="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src="https://images.unsplash.com/photo-1551434678-e076c223a692?auto=format&fit=crop&w=800&q=80"
                      alt="Engineering Phase"
                      class="w-full h-64 object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Step 4 -->
            <div class="relative" data-aos="fade-up" data-aos-delay="400">
              <div
                class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"
              ></div>

              <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0 md:order-2">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                  >
                    <h3
                      class="text-xl font-bold text-gray-900 mb-3 flex items-center"
                    >
                      <div
                        class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-4 flex-shrink-0"
                      >
                        <span class="text-primary-600 font-bold">4</span>
                      </div>
                      <span>Launch & Growth Enablement</span>
                    </h3>
                    <p class="text-gray-600 mb-4">
                      We ensure a smooth launch and provide ongoing support to
                      maximize ROI and drive sustainable growth.
                    </p>
                    <ul class="space-y-2">
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Performance monitoring dashboards, SEO/ASO
                          readiness</span
                        >
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span>A/B tested go-to-market campaigns</span>
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Feedback-driven iterations and lifecycle
                          automation</span
                        >
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="md:w-1/2 md:pl-12 md:order-1">
                  <div class="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=800&q=80"
                      alt="Launch Phase"
                      class="w-full h-64 object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section with Animated Counters -->
    <section
      class="py-16 bg-gradient-purple text-white relative overflow-hidden"
    >
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <div
          class="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-white to-transparent opacity-10"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-white to-transparent opacity-10"
        ></div>

        <!-- Abstract Pattern Background -->
        <div class="absolute inset-0 opacity-10">
          <svg width="100%" height="100%">
            <pattern
              id="pattern-hexagons"
              x="0"
              y="0"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <path
                id="pattern-hexagon"
                d="M20,0 L40,10 L40,30 L20,40 L0,30 L0,10 Z"
                fill="none"
                stroke="white"
                stroke-width="1"
              ></path>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#pattern-hexagons)"
            ></rect>
          </svg>
        </div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- Stat 1 -->
          <div class="text-center" data-aos="fade-up" data-aos-delay="100">
            <div
              class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
                ></path>
              </svg>
            </div>
            <h3 class="text-4xl font-bold mb-2">
              <span class="counter" data-target="500">0</span>+
            </h3>
            <p class="text-white/80">Happy Clients</p>
          </div>

          <!-- Stat 2 -->
          <div class="text-center" data-aos="fade-up" data-aos-delay="200">
            <div
              class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                  clip-rule="evenodd"
                ></path>
                <path
                  d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"
                ></path>
              </svg>
            </div>
            <h3 class="text-4xl font-bold mb-2">
              <span class="counter" data-target="850">0</span>+
            </h3>
            <p class="text-white/80">Projects Completed</p>
          </div>

          <!-- Stat 3 -->
          <div class="text-center" data-aos="fade-up" data-aos-delay="300">
            <div
              class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <h3 class="text-4xl font-bold mb-2">
              <span class="counter" data-target="8">0</span>+
            </h3>
            <p class="text-white/80">Years Experience</p>
          </div>

          <!-- Stat 4 -->
          <div class="text-center" data-aos="fade-up" data-aos-delay="400">
            <div
              class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
                ></path>
              </svg>
            </div>
            <h3 class="text-4xl font-bold mb-2">
              <span class="counter" data-target="35">0</span>+
            </h3>
            <p class="text-white/80">Expert Team Members</p>
          </div>
        </div>
      </div>
    </section>
    <!-- Web Development Services Showcase -->
    <section
      id="web-development-services"
      class="py-24 relative overflow-hidden"
    >
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blobs -->
        <div
          class="absolute top-40 right-20 w-96 h-96 bg-gradient-to-br from-blue-300/30 to-purple-600/30 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"
        ></div>
        <div
          class="absolute -bottom-40 -left-20 w-96 h-96 bg-gradient-to-tr from-indigo-300/30 to-blue-600/30 rounded-full filter blur-3xl opacity-30 animate-float"
          style="animation-delay: 1.5s"
        ></div>

        <!-- Decorative Grid Pattern -->
        <div class="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <pattern
              id="web-dev-grid"
              x="0"
              y="0"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <rect
                id="web-dev-square"
                width="20"
                height="20"
                x="0"
                y="0"
                fill="none"
                stroke="#5b21b6"
                stroke-width="0.5"
                rx="2"
                ry="2"
              />
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#web-dev-grid)"
            ></rect>
          </svg>
        </div>

        <!-- Code Pattern Background SVG -->
        <div class="absolute right-0 bottom-0 w-1/3 h-1/3 opacity-10">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="text-primary-600 w-full h-full"
          >
            <polyline points="16 18 22 12 16 6"></polyline>
            <polyline points="8 6 2 12 8 18"></polyline>
          </svg>
        </div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-16" data-aos="fade-up">
          <span
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-blue-700 uppercase rounded-full bg-blue-100 mb-4"
          >
            Web Development Services
          </span>
          <h2 class="text-3xl md:text-5xl font-extrabold text-gray-900 mb-4">
            Crafting
            <span
              class="text-gradient bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
              >Digital Experiences</span
            >
            That Drive Results
          </h2>
          <p class="text-xl text-gray-600">
            We build websites and web applications that not only look stunning
            but also perform exceptionally, driving business growth and
            enhancing user satisfaction.
          </p>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          <!-- Left Column: Content -->
          <div data-aos="fade-right" data-aos-delay="100">
            <h3 class="text-2xl font-bold text-gray-900 mb-6">
              Expert Web Development Solutions for Every Business Need
            </h3>
            <p class="text-gray-600 mb-8">
              At RayDesign Technologies, we don't just build websites - we
              engineer digital experiences that solve business problems, engage
              your audience, and drive measurable results. Our expert team
              combines cutting-edge technology with creative design to deliver
              solutions that stand out in today's competitive digital landscape.
            </p>

            <!-- Service Cards -->
            <div class="space-y-6">
              <!-- Responsive Web Design Card -->
              <div
                class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-all duration-300 border border-gray-100 transform hover:-translate-y-1 gradient-border"
              >
                <div class="flex items-start">
                  <div
                    class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0"
                  >
                    <i class="fas fa-desktop text-blue-600 text-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">
                      Responsive Web Design
                    </h4>
                    <p class="text-gray-600">
                      Websites that look and function flawlessly across all
                      devices - from desktops to smartphones - ensuring your
                      users have an optimal experience regardless of how they
                      access your site.
                    </p>
                    <div class="mt-3 flex space-x-3">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >Mobile-First</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                        >Cross-Browser</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                        >Adaptive</span
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- Custom Web Applications Card -->
              <div
                class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-all duration-300 border border-gray-100 transform hover:-translate-y-1 gradient-border"
              >
                <div class="flex items-start">
                  <div
                    class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0"
                  >
                    <i class="fas fa-cogs text-blue-600 text-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">
                      Custom Web Applications
                    </h4>
                    <p class="text-gray-600">
                      Tailor-made web applications that automate processes,
                      improve efficiency, and solve your unique business
                      challenges with intuitive interfaces and powerful
                      functionality.
                    </p>
                    <div class="mt-3 flex space-x-3">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >Scalable</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                        >Secure</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                        >Cloud-Based</span
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- E-Commerce Solutions Card -->
              <div
                class="bg-white rounded-xl p-6 shadow-soft hover:shadow-lg transition-all duration-300 border border-gray-100 transform hover:-translate-y-1 gradient-border"
              >
                <div class="flex items-start">
                  <div
                    class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0"
                  >
                    <i class="fas fa-shopping-cart text-blue-600 text-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">
                      E-Commerce Development
                    </h4>
                    <p class="text-gray-600">
                      Powerful online stores that convert visitors into
                      customers with seamless checkout experiences, product
                      management, and integration with payment gateways and
                      shipping providers.
                    </p>
                    <div class="mt-3 flex space-x-3">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >Shopify</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                        >WooCommerce</span
                      >
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                        >Custom</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- CTA Button -->
            <div class="mt-8">
              <a
                href="#web-dev-contact"
                class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-to-r from-blue-600 to-purple-600 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <span>Start Your Web Project Today</span>
                <svg
                  class="ml-2 -mr-1 w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </a>
            </div>
          </div>

          <!-- Right Column: Image & Interactive Elements -->
          <div class="relative" data-aos="fade-left" data-aos-delay="200">
            <!-- Main Showcase Image with Interactive Elements -->
            <div class="relative">
              <!-- Website Mockup -->
              <div
                class="relative z-10 rounded-2xl shadow-xl overflow-hidden bg-white p-2 animate-float"
              >
                <img
                  src="https://images.unsplash.com/photo-1517292987719-0369a794ec0f?auto=format&fit=crop&w=800&q=80"
                  alt="Web Development Showcase"
                  class="w-full h-auto rounded-xl"
                />

                <!-- Code Overlay -->
                <div
                  class="absolute inset-0 bg-gradient-to-tr from-gray-900/70 to-transparent rounded-xl opacity-60"
                ></div>

                <!-- Browser Controls -->
                <div
                  class="absolute top-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md flex items-center"
                >
                  <div class="flex space-x-2 mr-3">
                    <div class="w-3 h-3 rounded-full bg-red-500"></div>
                    <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                  <div
                    class="flex-1 bg-gray-200 rounded-full h-6 flex items-center px-3"
                  >
                    <span class="text-xs text-gray-500 truncate"
                      >https://www.raydesigntechnologies.com</span
                    >
                  </div>
                </div>

                <!-- Code Editor Element -->
                <div
                  class="absolute bottom-8 right-8 w-56 rounded-lg overflow-hidden shadow-lg animate__animated animate__fadeInRight"
                >
                  <div
                    class="bg-gray-800 text-white px-3 py-2 text-xs font-mono flex justify-between items-center"
                  >
                    <span>script.js</span>
                    <div class="flex space-x-2">
                      <i class="fas fa-minus"></i>
                      <i class="fas fa-square"></i>
                      <i class="fas fa-times"></i>
                    </div>
                  </div>
                  <div class="code-block p-3 text-xs font-mono">
                    <pre><span class="keyword">const</span> <span class="variable">app</span> = {
  <span class="variable">init</span>: <span class="function">function</span>() {
    <span class="keyword">this</span>.<span class="variable">bindEvents</span>();
    <span class="keyword">this</span>.<span class="variable">renderUI</span>();
  },
  <span class="variable">bindEvents</span>: <span class="function">function</span>() {
    <span class="comment">// Event handling</span>
  }
};</pre>
                  </div>
                </div>

                <!-- Responsive Design Indicator -->
                <div
                  class="absolute bottom-8 left-8 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg animate__animated animate__fadeInLeft"
                >
                  <div class="flex items-center">
                    <div class="flex space-x-2 mr-3">
                      <i class="fas fa-desktop text-blue-600"></i>
                      <i class="fas fa-tablet-alt text-indigo-600"></i>
                      <i class="fas fa-mobile-alt text-purple-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-800"
                      >Responsive Design</span
                    >
                  </div>
                  <div
                    class="mt-2 w-full bg-gray-200 rounded-full h-2 overflow-hidden"
                  >
                    <div
                      class="bg-gradient-to-r from-blue-600 to-purple-600 h-full w-3/4 rounded-full animate-pulse"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- Floating Elements -->
              <div
                class="absolute -top-6 -right-6 bg-white rounded-xl shadow-lg p-4 animate-float"
                style="animation-delay: 0.5s"
              >
                <div class="flex items-center space-x-2">
                  <div
                    class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"
                  >
                    <i class="fas fa-bolt text-blue-600"></i>
                  </div>
                  <div>
                    <p class="text-xs text-gray-500">Page Speed</p>
                    <p class="text-sm font-bold text-gray-800">98/100</p>
                  </div>
                </div>
              </div>

              <div
                class="absolute -bottom-6 -left-6 bg-white rounded-xl shadow-lg p-4 animate-float"
                style="animation-delay: 1s"
              >
                <div class="flex items-center space-x-2">
                  <div
                    class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center"
                  >
                    <i class="fas fa-code text-purple-600"></i>
                  </div>
                  <div>
                    <p class="text-xs text-gray-500">Clean Code</p>
                    <p class="text-sm font-bold text-gray-800">Validated</p>
                  </div>
                </div>
              </div>

              <!-- Technology Stack Icons -->
              <div class="absolute -bottom-4 right-20 flex space-x-2">
                <div
                  class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center animate-bounce-slow"
                  style="animation-delay: 0s"
                >
                  <i class="fab fa-html5 text-orange-500"></i>
                </div>
                <div
                  class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center animate-bounce-slow"
                  style="animation-delay: 0.2s"
                >
                  <i class="fab fa-css3-alt text-blue-500"></i>
                </div>
                <div
                  class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center animate-bounce-slow"
                  style="animation-delay: 0.4s"
                >
                  <i class="fab fa-js text-yellow-500"></i>
                </div>
                <div
                  class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center animate-bounce-slow"
                  style="animation-delay: 0.6s"
                >
                  <i class="fab fa-react text-blue-400"></i>
                </div>
                <div
                  class="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center animate-bounce-slow"
                  style="animation-delay: 0.8s"
                >
                  <i class="fab fa-node-js text-green-600"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Technology Stack Section -->
        <div class="mb-20" data-aos="fade-up" data-aos-delay="300">
          <h3 class="text-2xl font-bold text-gray-900 text-center mb-12">
            Our Technology Stack
          </h3>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Frontend Technologies -->
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 group"
            >
              <div class="relative h-40 overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1629904853716-f0bc54eea481?auto=format&fit=crop&w=600&q=80"
                  alt="Frontend Development"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-blue-900/80 to-blue-600/40 opacity-80"
                ></div>
                <div class="absolute inset-0 flex flex-col justify-end p-6">
                  <div class="flex items-center mb-4">
                    <i class="fas fa-laptop-code text-2xl text-white mr-3"></i>
                    <h4 class="text-xl font-bold text-white">Frontend</h4>
                  </div>
                  <p class="text-white/90">
                    Creating engaging, interactive user interfaces with
                    cutting-edge technologies.
                  </p>
                </div>
              </div>

              <div class="p-6">
                <div class="flex flex-wrap gap-3 mb-6">
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-html5 text-orange-500 mr-1"></i>
                    <span>HTML5</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-css3-alt text-blue-500 mr-1"></i>
                    <span>CSS3/SASS</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-js text-yellow-500 mr-1"></i>
                    <span>JavaScript</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-react text-blue-400 mr-1"></i>
                    <span>React</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-vuejs text-green-500 mr-1"></i>
                    <span>Vue.js</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-angular text-red-500 mr-1"></i>
                    <span>Angular</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <img
                      src="https://tailwindcss.com/favicon-32x32.png"
                      alt="Tailwind"
                      class="w-4 h-4 mr-1"
                    />
                    <span>Tailwind CSS</span>
                  </div>
                </div>

                <div class="border-t border-gray-100 pt-4">
                  <h5 class="text-sm font-semibold text-gray-700 mb-2">
                    Why It Matters:
                  </h5>
                  <p class="text-gray-600 text-sm">
                    Modern frontend technologies enable us to create
                    fast-loading, interactive, and visually stunning interfaces
                    that engage your users and drive conversions.
                  </p>
                </div>
              </div>
            </div>

            <!-- Backend Technologies -->
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 group"
            >
              <div class="relative h-40 overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?auto=format&fit=crop&w=600&q=80"
                  alt="Backend Development"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-indigo-600/40 opacity-80"
                ></div>
                <div class="absolute inset-0 flex flex-col justify-end p-6">
                  <div class="flex items-center mb-4">
                    <i class="fas fa-server text-2xl text-white mr-3"></i>
                    <h4 class="text-xl font-bold text-white">Backend</h4>
                  </div>
                  <p class="text-white/90">
                    Building robust server-side solutions that power your
                    applications with security and performance.
                  </p>
                </div>
              </div>

              <div class="p-6">
                <div class="flex flex-wrap gap-3 mb-6">
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-node-js text-green-600 mr-1"></i>
                    <span>Node.js</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-php text-indigo-500 mr-1"></i>
                    <span>PHP/Laravel</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-python text-blue-500 mr-1"></i>
                    <span>Python</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <img
                      src="https://expressjs.com/images/favicon.png"
                      alt="Express"
                      class="w-4 h-4 mr-1"
                    />
                    <span>Express.js</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fas fa-gem text-red-500 mr-1"></i>
                    <span>Ruby on Rails</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <img
                      src="https://www.djangoproject.com/favicon.ico"
                      alt="Django"
                      class="w-4 h-4 mr-1"
                    />
                    <span>Django</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fas fa-database text-blue-600 mr-1"></i>
                    <span>RESTful APIs</span>
                  </div>
                </div>

                <div class="border-t border-gray-100 pt-4">
                  <h5 class="text-sm font-semibold text-gray-700 mb-2">
                    Why It Matters:
                  </h5>
                  <p class="text-gray-600 text-sm">
                    Robust backend systems provide the foundation for secure,
                    scalable applications that can handle everything from user
                    authentication to complex business logic.
                  </p>
                </div>
              </div>
            </div>

            <!-- Database & Infrastructure -->
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 group"
            >
              <div class="relative h-40 overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?auto=format&fit=crop&w=600&q=80"
                  alt="Database & Infrastructure"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-purple-600/40 opacity-80"
                ></div>
                <div class="absolute inset-0 flex flex-col justify-end p-6">
                  <div class="flex items-center mb-4">
                    <i class="fas fa-database text-2xl text-white mr-3"></i>
                    <h4 class="text-xl font-bold text-white">
                      Database & Infrastructure
                    </h4>
                  </div>
                  <p class="text-white/90">
                    Optimizing data storage and cloud solutions for maximum
                    performance and reliability.
                  </p>
                </div>
              </div>

              <div class="p-6">
                <div class="flex flex-wrap gap-3 mb-6">
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <img
                      src="https://www.mongodb.com/assets/images/global/favicon.ico"
                      alt="MongoDB"
                      class="w-4 h-4 mr-1"
                    />
                    <span>MongoDB</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <img
                      src="https://www.mysql.com/common/logos/logo-mysql-170x115.png"
                      alt="MySQL"
                      class="w-4 h-4 mr-1"
                    />
                    <span>MySQL</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <img
                      src="https://www.postgresql.org/media/img/about/press/elephant.png"
                      alt="PostgreSQL"
                      class="w-4 h-4 mr-1"
                    />
                    <span>PostgreSQL</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-aws text-orange-500 mr-1"></i>
                    <span>AWS</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-google text-blue-500 mr-1"></i>
                    <span>Google Cloud</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-microsoft text-blue-600 mr-1"></i>
                    <span>Azure</span>
                  </div>
                  <div
                    class="tech-badge flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
                  >
                    <i class="fab fa-docker text-blue-500 mr-1"></i>
                    <span>Docker</span>
                  </div>
                </div>

                <div class="border-t border-gray-100 pt-4">
                  <h5 class="text-sm font-semibold text-gray-700 mb-2">
                    Why It Matters:
                  </h5>
                  <p class="text-gray-600 text-sm">
                    Efficient data management and cloud infrastructure ensure
                    your web applications are fast, reliable, and can scale with
                    your business growth.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Success Stories Slider -->
        <div class="mb-20" data-aos="fade-up" data-aos-delay="400">
          <h3 class="text-2xl font-bold text-gray-900 text-center mb-4">
            Web Development Success Stories
          </h3>
          <p class="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            See how our web development expertise has helped businesses
            transform their digital presence and achieve remarkable results.
          </p>

          <div class="relative overflow-hidden">
            <!-- Success Stories Slider -->
            <div
              class="web-success-slider flex transition-transform duration-500"
              id="webSuccessSlider"
            >
              <!-- Success Story 1 -->
              <div class="success-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft overflow-hidden h-full border border-gray-100"
                >
                  <div class="relative h-48 overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&w=600&q=80"
                      alt="E-commerce Website Case Study"
                      class="w-full h-full object-cover"
                    />
                    <div
                      class="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"
                    ></div>
                    <div class="absolute bottom-4 left-4">
                      <span
                        class="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-blue-700"
                        >E-commerce</span
                      >
                    </div>
                  </div>

                  <div class="p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-3">
                      LuxeMarket Online Store
                    </h4>
                    <p class="text-gray-600 mb-4">
                      Developed a custom e-commerce platform with advanced
                      filtering, personalized recommendations, and seamless
                      checkout, resulting in a 78% increase in conversion rate.
                    </p>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Conversion Rate</p>
                        <p class="text-xl font-bold text-blue-600">+78%</p>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Page Load Time</p>
                        <p class="text-xl font-bold text-blue-600">0.8s</p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="flex space-x-1">
                        <span
                          class="tech-badge bg-gray-100 rounded-md px-2 py-1 text-xs"
                          >React</span
                        >
                        <span
                          class="tech-badge bg-gray-100 rounded-md px-2 py-1 text-xs"
                          >Node.js</span
                        >
                      </div>
                      <a
                        href="#case-studies"
                        class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-300 text-sm"
                      >
                        Read Case Study
                        <svg
                          class="ml-2 w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          ></path>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Success Story 2 -->
              <div class="success-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft overflow-hidden h-full border border-gray-100"
                >
                  <div class="relative h-48 overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?auto=format&fit=crop&w=600&q=80"
                      alt="Healthcare Web App Case Study"
                      class="w-full h-full object-cover"
                    />
                    <div
                      class="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"
                    ></div>
                    <div class="absolute bottom-4 left-4">
                      <span
                        class="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-blue-700"
                        >Healthcare</span
                      >
                    </div>
                  </div>

                  <div class="p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-3">
                      MediConnect Patient Portal
                    </h4>
                    <p class="text-gray-600 mb-4">
                      Built a secure, HIPAA-compliant patient portal allowing
                      medical appointment scheduling, telemedicine visits, and
                      access to health records.
                    </p>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Administrative Time</p>
                        <p class="text-xl font-bold text-blue-600">-65%</p>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">
                          Patient Satisfaction
                        </p>
                        <p class="text-xl font-bold text-blue-600">92%</p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="flex space-x-1">
                        <span
                          class="tech-badge bg-gray-100 rounded-md px-2 py-1 text-xs"
                          >Angular</span
                        >
                        <span
                          class="tech-badge bg-gray-100 rounded-md px-2 py-1 text-xs"
                          >Firebase</span
                        >
                      </div>
                      <a
                        href="#case-studies"
                        class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-300 text-sm"
                      >
                        Read Case Study
                        <svg
                          class="ml-2 w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          ></path>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Success Story 3 -->
              <div class="success-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft overflow-hidden h-full border border-gray-100"
                >
                  <div class="relative h-48 overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=600&q=80"
                      alt="SaaS Dashboard Case Study"
                      class="w-full h-full object-cover"
                    />
                    <div
                      class="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"
                    ></div>
                    <div class="absolute bottom-4 left-4">
                      <span
                        class="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-blue-700"
                        >SaaS Platform</span
                      >
                    </div>
                  </div>

                  <div class="p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-3">
                      ProjectFlow Management Tool
                    </h4>
                    <p class="text-gray-600 mb-4">
                      Developed a full-featured project management SaaS
                      application with real-time collaboration, task tracking,
                      and automated reporting.
                    </p>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">User Onboarding</p>
                        <p class="text-xl font-bold text-blue-600">+230%</p>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Retention Rate</p>
                        <p class="text-xl font-bold text-blue-600">87%</p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="flex space-x-1">
                        <span
                          class="tech-badge bg-gray-100 rounded-md px-2 py-1 text-xs"
                          >Vue.js</span
                        >
                        <span
                          class="tech-badge bg-gray-100 rounded-md px-2 py-1 text-xs"
                          >Laravel</span
                        >
                      </div>
                      <a
                        href="#case-studies"
                        class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-300 text-sm"
                      >
                        Read Case Study
                        <svg
                          class="ml-2 w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          ></path>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Success Story 4 -->
              <div class="success-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft overflow-hidden h-full border border-gray-100"
                >
                  <div class="relative h-48 overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1524178232363-1fb2b075b655?auto=format&fit=crop&w=600&q=80"
                      alt="Educational Platform Case Study"
                      class="w-full h-full object-cover"
                    />
                    <div
                      class="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"
                    ></div>
                    <div class="absolute bottom-4 left-4">
                      <span
                        class="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-blue-700"
                        >Education</span
                      >
                    </div>
                  </div>

                  <div class="p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-3">
                      LearnHub Online Academy
                    </h4>
                    <p class="text-gray-600 mb-4">
                      Created a comprehensive learning management system with
                      interactive courses, progress tracking, and certification
                      management.
                    </p>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Course Completion</p>
                        <p class="text-xl font-bold text-blue-600">+83%</p>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <p class="text-sm text-gray-500">Student Engagement</p>
                        <p class="text-xl font-bold text-blue-600">+156%</p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between">
                      <div class="flex space-x-1">
                        <span
                          class="tech-badge bg-gray-100 rounded-md px-2 py-1 text-xs"
                          >React</span
                        >
                        <span
                          class="tech-badge bg-gray-100 rounded-md px-2 py-1 text-xs"
                          >Django</span
                        >
                      </div>
                      <a
                        href="#case-studies"
                        class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-300 text-sm"
                      >
                        Read Case Study
                        <svg
                          class="ml-2 w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"
                          ></path>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Slider Controls -->
            <div class="flex justify-center items-center mt-8">
              <button
                id="prevSuccessStory"
                class="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-300 mr-4 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  ></path>
                </svg>
              </button>

              <div class="flex space-x-2">
                <button
                  class="success-dot w-3 h-3 rounded-full bg-blue-600"
                ></button>
                <button
                  class="success-dot w-3 h-3 rounded-full bg-gray-300"
                ></button>
                <button
                  class="success-dot w-3 h-3 rounded-full bg-gray-300"
                ></button>
                <button
                  class="success-dot w-3 h-3 rounded-full bg-gray-300"
                ></button>
              </div>

              <button
                id="nextSuccessStory"
                class="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-300 ml-4 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Web Development Process -->
        <div class="mb-20" data-aos="fade-up" data-aos-delay="500">
          <h3 class="text-2xl font-bold text-gray-900 text-center mb-4">
            Our Web Development Process
          </h3>
          <p class="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            We follow a structured, transparent development methodology to
            ensure your web project is delivered on time, on budget, and exceeds
            expectations.
          </p>

          <!-- Process Steps -->
          <div class="relative">
            <!-- Connecting Line -->
            <div
              class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-blue-400 to-purple-600 rounded-full hidden md:block"
              style="z-index: 1"
            ></div>

            <div class="space-y-12">
              <!-- Process Step 1 -->
              <div class="relative" data-aos="fade-up" data-aos-delay="100">
                <div
                  class="hidden md:block absolute top-6 left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-white border-4 border-blue-500 z-10"
                ></div>

                <div class="flex flex-col md:flex-row items-center">
                  <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
                    <div
                      class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                    >
                      <h3
                        class="text-xl font-bold text-gray-900 mb-3 flex items-center justify-end"
                      >
                        <span>Discovery & Requirements</span>
                        <div
                          class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center ml-4 flex-shrink-0"
                        >
                          <span class="text-blue-600 font-bold">1</span>
                        </div>
                      </h3>
                      <p class="text-gray-600 mb-4">
                        We begin by understanding your business goals, target
                        audience, and project requirements through in-depth
                        consultations and research.
                      </p>
                      <ul class="space-y-2 text-right">
                        <li class="flex items-center justify-end">
                          <span
                            >Stakeholder interviews & requirement
                            gathering</span
                          >
                          <svg
                            class="w-5 h-5 text-blue-500 ml-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                        </li>
                        <li class="flex items-center justify-end">
                          <span>Competitor analysis & market research</span>
                          <svg
                            class="w-5 h-5 text-blue-500 ml-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                        </li>
                        <li class="flex items-center justify-end">
                          <span>Project scope definition & specification</span>
                          <svg
                            class="w-5 h-5 text-blue-500 ml-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="md:w-1/2 md:pl-12">
                    <div class="rounded-2xl overflow-hidden shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1600880292089-90a7e086ee0c?auto=format&fit=crop&w=800&q=80"
                        alt="Discovery Phase"
                        class="w-full h-64 object-cover"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Process Step 2 -->
              <div class="relative" data-aos="fade-up" data-aos-delay="200">
                <div
                  class="hidden md:block absolute top-6 left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-white border-4 border-indigo-500 z-10"
                ></div>

                <div class="flex flex-col md:flex-row items-center">
                  <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0 md:order-2">
                    <div
                      class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                    >
                      <h3
                        class="text-xl font-bold text-gray-900 mb-3 flex items-center"
                      >
                        <div
                          class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-4 flex-shrink-0"
                        >
                          <span class="text-indigo-600 font-bold">2</span>
                        </div>
                        <span>Design & Prototyping</span>
                      </h3>
                      <p class="text-gray-600 mb-4">
                        We create wireframes and interactive prototypes to
                        visualize the user interface and experience, ensuring
                        alignment with your brand and business objectives.
                      </p>
                      <ul class="space-y-2">
                        <li class="flex items-center">
                          <svg
                            class="w-5 h-5 text-indigo-500 mr-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                          <span>Wireframing & UI/UX design</span>
                        </li>
                        <li class="flex items-center">
                          <svg
                            class="w-5 h-5 text-indigo-500 mr-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                          <span>Interactive prototyping</span>
                        </li>
                        <li class="flex items-center">
                          <svg
                            class="w-5 h-5 text-indigo-500 mr-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                          <span>User experience optimization</span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="md:w-1/2 md:pl-12 md:order-1">
                    <div class="rounded-2xl overflow-hidden shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?auto=format&fit=crop&w=800&q=80"
                        alt="Design Phase"
                        class="w-full h-64 object-cover"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Process Step 3 -->
              <div class="relative" data-aos="fade-up" data-aos-delay="300">
                <div
                  class="hidden md:block absolute top-6 left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-white border-4 border-purple-500 z-10"
                ></div>

                <div class="flex flex-col md:flex-row items-center">
                  <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
                    <div
                      class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                    >
                      <h3
                        class="text-xl font-bold text-gray-900 mb-3 flex items-center justify-end"
                      >
                        <span>Development & Integration</span>
                        <div
                          class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center ml-4 flex-shrink-0"
                        >
                          <span class="text-purple-600 font-bold">3</span>
                        </div>
                      </h3>
                      <p class="text-gray-600 mb-4">
                        Our development team brings designs to life with clean,
                        efficient code, implementing all functionality and
                        integrating with necessary systems and APIs.
                      </p>
                      <ul class="space-y-2 text-right">
                        <li class="flex items-center justify-end">
                          <span>Frontend & backend development</span>
                          <svg
                            class="w-5 h-5 text-purple-500 ml-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                        </li>
                        <li class="flex items-center justify-end">
                          <span>API & third-party integrations</span>
                          <svg
                            class="w-5 h-5 text-purple-500 ml-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                        </li>
                        <li class="flex items-center justify-end">
                          <span>Database architecture & implementation</span>
                          <svg
                            class="w-5 h-5 text-purple-500 ml-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="md:w-1/2 md:pl-12">
                    <div class="rounded-2xl overflow-hidden shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1484417894907-623942c8ee29?auto=format&fit=crop&w=800&q=80"
                        alt="Development Phase"
                        class="w-full h-64 object-cover"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Process Step 4 -->
              <div class="relative" data-aos="fade-up" data-aos-delay="400">
                <div
                  class="hidden md:block absolute top-6 left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-white border-4 border-blue-500 z-10"
                ></div>

                <div class="flex flex-col md:flex-row items-center">
                  <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0 md:order-2">
                    <div
                      class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                    >
                      <h3
                        class="text-xl font-bold text-gray-900 mb-3 flex items-center"
                      >
                        <div
                          class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0"
                        >
                          <span class="text-blue-600 font-bold">4</span>
                        </div>
                        <span>Testing & Quality Assurance</span>
                      </h3>
                      <p class="text-gray-600 mb-4">
                        Rigorous testing across devices, browsers, and user
                        scenarios ensures your web solution is bug-free, secure,
                        and delivers the intended experience.
                      </p>
                      <ul class="space-y-2">
                        <li class="flex items-center">
                          <svg
                            class="w-5 h-5 text-blue-500 mr-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                          <span>Functional & cross-browser testing</span>
                        </li>
                        <li class="flex items-center">
                          <svg
                            class="w-5 h-5 text-blue-500 mr-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                          <span>Performance & security testing</span>
                        </li>
                        <li class="flex items-center">
                          <svg
                            class="w-5 h-5 text-blue-500 mr-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                          <span>User acceptance testing</span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="md:w-1/2 md:pl-12 md:order-1">
                    <div class="rounded-2xl overflow-hidden shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1581093458791-9d42cc509945?auto=format&fit=crop&w=800&q=80"
                        alt="Testing Phase"
                        class="w-full h-64 object-cover"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Process Step 5 -->
              <div class="relative" data-aos="fade-up" data-aos-delay="500">
                <div
                  class="hidden md:block absolute top-6 left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-white border-4 border-indigo-500 z-10"
                ></div>

                <div class="flex flex-col md:flex-row items-center">
                  <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
                    <div
                      class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                    >
                      <h3
                        class="text-xl font-bold text-gray-900 mb-3 flex items-center justify-end"
                      >
                        <span>Deployment & Ongoing Support</span>
                        <div
                          class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center ml-4 flex-shrink-0"
                        >
                          <span class="text-indigo-600 font-bold">5</span>
                        </div>
                      </h3>
                      <p class="text-gray-600 mb-4">
                        We handle deployment to your hosting environment and
                        provide ongoing maintenance, updates, and support to
                        ensure your web solution continues to perform optimally.
                      </p>
                      <ul class="space-y-2 text-right">
                        <li class="flex items-center justify-end">
                          <span>Secure deployment & launch</span>
                          <svg
                            class="w-5 h-5 text-indigo-500 ml-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                        </li>
                        <li class="flex items-center justify-end">
                          <span>Performance monitoring & optimization</span>
                          <svg
                            class="w-5 h-5 text-indigo-500 ml-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                        </li>
                        <li class="flex items-center justify-end">
                          <span>Ongoing maintenance & updates</span>
                          <svg
                            class="w-5 h-5 text-indigo-500 ml-2 flex-shrink-0"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            ></path>
                          </svg>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="md:w-1/2 md:pl-12">
                    <div class="rounded-2xl overflow-hidden shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=800&q=80"
                        alt="Deployment Phase"
                        class="w-full h-64 object-cover"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Call to Action Banner -->
        <div
          class="relative rounded-3xl overflow-hidden"
          data-aos="fade-up"
          data-aos-delay="600"
        >
          <div class="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1551434678-e076c223a692?auto=format&fit=crop&w=1200&q=80"
              alt="Web Development"
              class="w-full h-full object-cover"
            />
            <div
              class="absolute inset-0 bg-gradient-to-r from-blue-900/90 to-purple-900/80"
            ></div>
          </div>

          <div class="relative p-8 md:p-12 lg:p-16">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h2 class="text-3xl md:text-4xl font-extrabold text-white mb-6">
                  Ready to Transform Your Digital Presence?
                </h2>
                <p class="text-white/90 text-xl mb-8">
                  Partner with our expert web development team to create a
                  stunning, high-performance website or web application that
                  drives real business results.
                </p>

                <ul class="space-y-4 mb-8">
                  <li class="flex items-start">
                    <svg
                      class="w-6 h-6 text-blue-400 mr-3 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-white"
                      >Dedicated project manager & development team</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-6 h-6 text-blue-400 mr-3 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-white"
                      >Transparent process & regular progress updates</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-6 h-6 text-blue-400 mr-3 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span class="text-white"
                      >Ongoing support & maintenance packages</span
                    >
                  </li>
                </ul>

                <a
                  href="#web-dev-contact"
                  id="webDevCTA"
                  class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-blue-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                >
                  <span>Get Your Free Web Development Consultation</span>
                  <svg
                    class="ml-2 -mr-1 w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </a>
              </div>

              <!-- Contact Form -->
              <div id="web-dev-contact">
                <div
                  class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20"
                >
                  <h3 class="text-xl font-bold text-white mb-4">
                    Request Your Free Consultation
                  </h3>
                  <p class="text-white/80 mb-6">
                    Fill out the form below and our web development experts will
                    analyze your needs and provide personalized recommendations.
                  </p>

                  <form
                    action="https://submit-form.com/l2ArscGWs"
                    class="space-y-4"
                  >
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label for="web-name" class="sr-only">Your Name</label>
                        <input
                          type="text"
                          id="web-name"
                          name="web-name"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                          placeholder="Your Name"
                        />
                      </div>
                      <div>
                        <label for="web-email" class="sr-only"
                          >Your Email</label
                        >
                        <input
                          type="email"
                          id="web-email"
                          name="web-email"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                          placeholder="Your Email"
                        />
                      </div>
                    </div>

                    <div>
                      <label for="web-company" class="sr-only"
                        >Company Name</label
                      >
                      <input
                        type="text"
                        id="web-company"
                        name="web-company"
                        class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                        placeholder="Company Name"
                      />
                    </div>

                    <div>
                      <label for="web-project-type" class="sr-only"
                        >Project Type</label
                      >
                      <select
                        id="web-project-type"
                        name="web-project-type"
                        class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                      >
                        <option
                          value=""
                          disabled
                          selected
                          class="text-gray-500"
                        >
                          Project Type
                        </option>
                        <option value="website" class="text-gray-800">
                          Website Design & Development
                        </option>
                        <option value="webapp" class="text-gray-800">
                          Web Application
                        </option>
                        <option value="ecommerce" class="text-gray-800">
                          E-Commerce Website
                        </option>
                        <option value="portal" class="text-gray-800">
                          Customer/Member Portal
                        </option>
                        <option value="redesign" class="text-gray-800">
                          Website Redesign
                        </option>
                        <option value="other" class="text-gray-800">
                          Other
                        </option>
                      </select>
                    </div>

                    <div>
                      <label for="web-message" class="sr-only"
                        >Project Details</label
                      >
                      <textarea
                        id="web-message"
                        name="web-message"
                        rows="3"
                        class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                        placeholder="Tell us about your project requirements..."
                      ></textarea>
                    </div>

                    <button
                      type="submit"
                      class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium rounded-lg text-blue-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                    >
                      Request Free Consultation
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- JavaScript for Web Development Services Section -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Success Stories Slider
        const successSlider = document.getElementById("webSuccessSlider");
        const successSlides = document.querySelectorAll(".success-slide");
        const successDots = document.querySelectorAll(".success-dot");
        const prevSuccessBtn = document.getElementById("prevSuccessStory");
        const nextSuccessBtn = document.getElementById("nextSuccessStory");
        let successCurrentIndex = 0;
        const successSlidesToShow = window.innerWidth >= 768 ? 3 : 1;
        const successSlideWidth = 100 / successSlidesToShow;

        // Initialize slider
        function initSuccessSlider() {
          successSlides.forEach((slide) => {
            slide.style.minWidth = `${successSlideWidth}%`;
          });
          goToSuccessSlide(0);
        }

        // Go to specific slide
        function goToSuccessSlide(index) {
          if (index < 0) {
            index = successSlides.length - successSlidesToShow;
          } else if (index > successSlides.length - successSlidesToShow) {
            index = 0;
          }

          successCurrentIndex = index;
          const translateValue = -index * successSlideWidth;
          successSlider.style.transform = `translateX(${translateValue}%)`;

          // Update dots
          successDots.forEach((dot, i) => {
            dot.classList.remove("bg-blue-600");
            dot.classList.add("bg-gray-300");

            if (i === Math.floor(index / successSlidesToShow)) {
              dot.classList.remove("bg-gray-300");
              dot.classList.add("bg-blue-600");
            }
          });
        }

        // Event listeners
        prevSuccessBtn.addEventListener("click", () => {
          goToSuccessSlide(successCurrentIndex - 1);
        });

        nextSuccessBtn.addEventListener("click", () => {
          goToSuccessSlide(successCurrentIndex + 1);
        });

        successDots.forEach((dot, i) => {
          dot.addEventListener("click", () => {
            goToSuccessSlide(i * successSlidesToShow);
          });
        });

        // Responsive adjustment
        window.addEventListener("resize", initSuccessSlider);

        // Initialize on load
        initSuccessSlider();
      });
    </script>
    <!-- Testimonials Section with Client Success Stories -->
    <section id="testimonials" class="py-20 bg-white relative overflow-hidden">
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Circles -->
        <div
          class="absolute top-40 right-20 w-80 h-80 bg-gradient-to-br from-primary-100 to-primary-300 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"
        ></div>
        <div
          class="absolute -bottom-40 -left-20 w-96 h-96 bg-gradient-to-tr from-secondary-100 to-secondary-300 rounded-full filter blur-3xl opacity-20 animate-float"
          style="animation-delay: 2s"
        ></div>

        <!-- SVG Wave Shape -->
        <svg
          class="absolute top-0 left-0 w-full transform rotate-180"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
        >
          <path
            fill="rgba(237, 233, 254, 0.5)"
            fill-opacity="1"
            d="M0,224L48,224C96,224,192,224,288,208C384,192,480,160,576,154.7C672,149,768,171,864,186.7C960,203,1056,213,1152,202.7C1248,192,1344,160,1392,144L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>

        <!-- Abstract Pattern Background -->
        <div class="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <pattern
              id="testimonial-dots"
              x="0"
              y="0"
              width="25"
              height="25"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <circle
                id="testimonial-dot"
                cx="12.5"
                cy="12.5"
                r="1.5"
                fill="#6d28d9"
              ></circle>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#testimonial-dots)"
            ></rect>
          </svg>
        </div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Section Header -->
        <div class="text-center max-w-3xl mx-auto mb-16" data-aos="fade-up">
          <span
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
          >
            Client Testimonials
          </span>
          <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">
            What Our <span class="text-gradient">Clients Say</span> About Us
          </h2>
          <p class="text-xl text-gray-600">
            Discover why businesses trust RayDesign Technologies to transform
            their digital presence and drive meaningful results.
          </p>
        </div>

        <!-- Testimonial Rating Overview -->
        <div
          class="bg-white rounded-2xl shadow-xl p-8 mb-16 border border-gray-100"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 class="text-2xl font-bold text-gray-900 mb-4">
                Our Client Satisfaction
              </h3>
              <p class="text-gray-600 mb-6">
                Based on
                <span class="font-semibold">250+ verified reviews</span> across
                multiple platforms, our clients rate us consistently high for
                quality, service, and results.
              </p>

              <!-- Rating Bars -->
              <div class="space-y-4">
                <div>
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700"
                      >Quality of Work</span
                    >
                    <span class="text-sm font-medium text-gray-700">4.9/5</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      class="bg-gradient-to-r from-primary-500 to-primary-700 h-2.5 rounded-full"
                      style="width: 98%"
                    ></div>
                  </div>
                </div>

                <div>
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700"
                      >Communication</span
                    >
                    <span class="text-sm font-medium text-gray-700">4.8/5</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      class="bg-gradient-to-r from-primary-500 to-primary-700 h-2.5 rounded-full"
                      style="width: 96%"
                    ></div>
                  </div>
                </div>

                <div>
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700"
                      >On-time Delivery</span
                    >
                    <span class="text-sm font-medium text-gray-700">4.7/5</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      class="bg-gradient-to-r from-primary-500 to-primary-700 h-2.5 rounded-full"
                      style="width: 94%"
                    ></div>
                  </div>
                </div>

                <div>
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700"
                      >Value for Money</span
                    >
                    <span class="text-sm font-medium text-gray-700">4.6/5</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      class="bg-gradient-to-r from-primary-500 to-primary-700 h-2.5 rounded-full"
                      style="width: 92%"
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <!-- Rating Card 1 -->
              <div class="bg-primary-50 rounded-xl p-6 text-center">
                <div class="text-4xl font-bold text-primary-600 mb-2">98%</div>
                <p class="text-sm text-gray-600">Client Retention Rate</p>
              </div>

              <!-- Rating Card 2 -->
              <div class="bg-secondary-50 rounded-xl p-6 text-center">
                <div class="text-4xl font-bold text-secondary-600 mb-2">
                  4.9
                </div>
                <div class="flex justify-center">
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                  <svg
                    class="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    ></path>
                  </svg>
                </div>
                <p class="text-sm text-gray-600 mt-1">Average Rating</p>
              </div>

              <!-- Rating Card 3 -->
              <div class="bg-green-50 rounded-xl p-6 text-center">
                <div class="text-4xl font-bold text-green-600 mb-2">92%</div>
                <p class="text-sm text-gray-600">Referral Rate</p>
              </div>

              <!-- Rating Card 4 -->
              <div class="bg-blue-50 rounded-xl p-6 text-center">
                <div class="text-4xl font-bold text-blue-600 mb-2">32</div>
                <p class="text-sm text-gray-600">Industry Awards</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Testimonial Slider -->
        <div class="mb-16" data-aos="fade-up" data-aos-delay="200">
          <div class="testimonial-slider overflow-hidden">
            <div
              class="testimonial-slider-wrapper flex transition-transform duration-500"
              id="testimonialWrapper"
            >
              <!-- Testimonial Card 1 -->
              <div class="testimonial-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft hover:shadow-lg transition-all duration-300 p-8 h-full border border-gray-100"
                >
                  <div class="flex items-center mb-6">
                    <div class="flex space-x-1">
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                    </div>
                  </div>

                  <div class="mb-6">
                    <div class="relative mb-2">
                      <i
                        class="fas fa-quote-left text-4xl text-primary-100 absolute -top-4 -left-2"
                      ></i>
                      <p class="text-gray-600 relative z-10">
                        RayDesign Technologies transformed our outdated website
                        into a modern, user-friendly platform that has
                        significantly increased our conversion rates. Their
                        team's expertise in both design and development is truly
                        impressive, and their strategic approach to our business
                        needs exceeded our expectations.
                      </p>
                    </div>
                    <p class="text-sm text-gray-500 italic">
                      Project: E-commerce Website Redesign
                    </p>
                  </div>

                  <div class="flex items-center">
                    <img
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=100&h=100&q=80"
                      alt="John Smith"
                      class="w-12 h-12 rounded-full object-cover mr-4"
                    />
                    <div>
                      <h4 class="font-semibold text-gray-900">John Smith</h4>
                      <p class="text-sm text-gray-500">
                        CEO, TechRetail Solutions
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Testimonial Card 2 -->
              <div class="testimonial-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft hover:shadow-lg transition-all duration-300 p-8 h-full border border-gray-100"
                >
                  <div class="flex items-center mb-6">
                    <div class="flex space-x-1">
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                    </div>
                  </div>

                  <div class="mb-6">
                    <div class="relative mb-2">
                      <i
                        class="fas fa-quote-left text-4xl text-primary-100 absolute -top-4 -left-2"
                      ></i>
                      <p class="text-gray-600 relative z-10">
                        The mobile app developed by RayDesign Technologies has
                        revolutionized how we interact with our patients. The
                        intuitive UI/UX design and robust functionality have
                        received overwhelmingly positive feedback. Their team's
                        attention to detail and commitment to understanding our
                        specific healthcare needs made all the difference.
                      </p>
                    </div>
                    <p class="text-sm text-gray-500 italic">
                      Project: Healthcare Mobile Application
                    </p>
                  </div>

                  <div class="flex items-center">
                    <img
                      src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?auto=format&fit=crop&w=100&h=100&q=80"
                      alt="Sarah Johnson"
                      class="w-12 h-12 rounded-full object-cover mr-4"
                    />
                    <div>
                      <h4 class="font-semibold text-gray-900">Sarah Johnson</h4>
                      <p class="text-sm text-gray-500">
                        Director, HealthFirst Clinic
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Testimonial Card 3 -->
              <div class="testimonial-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft hover:shadow-lg transition-all duration-300 p-8 h-full border border-gray-100"
                >
                  <div class="flex items-center mb-6">
                    <div class="flex space-x-1">
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                    </div>
                  </div>

                  <div class="mb-6">
                    <div class="relative mb-2">
                      <i
                        class="fas fa-quote-left text-4xl text-primary-100 absolute -top-4 -left-2"
                      ></i>
                      <p class="text-gray-600 relative z-10">
                        Working with RayDesign Technologies on our brand
                        identity was a game-changer for our company. Their
                        creative team captured the essence of our vision and
                        translated it into a stunning visual identity that
                        perfectly represents our values. The comprehensive brand
                        guidelines they delivered have been invaluable for
                        maintaining consistency across all our channels.
                      </p>
                    </div>
                    <p class="text-sm text-gray-500 italic">
                      Project: Brand Identity & Guidelines
                    </p>
                  </div>

                  <div class="flex items-center">
                    <img
                      src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=100&h=100&q=80"
                      alt="Michael Brown"
                      class="w-12 h-12 rounded-full object-cover mr-4"
                    />
                    <div>
                      <h4 class="font-semibold text-gray-900">Michael Brown</h4>
                      <p class="text-sm text-gray-500">
                        Marketing Director, EcoVision
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Testimonial Card 4 -->
              <div class="testimonial-slide min-w-full md:min-w-[33.333%] px-4">
                <div
                  class="bg-white rounded-2xl shadow-soft hover:shadow-lg transition-all duration-300 p-8 h-full border border-gray-100"
                >
                  <div class="flex items-center mb-6">
                    <div class="flex space-x-1">
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                      <svg
                        class="w-5 h-5 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                        ></path>
                      </svg>
                    </div>
                  </div>

                  <div class="mb-6">
                    <div class="relative mb-2">
                      <i
                        class="fas fa-quote-left text-4xl text-primary-100 absolute -top-4 -left-2"
                      ></i>
                      <p class="text-gray-600 relative z-10">
                        Since implementing the AI chatbot developed by RayDesign
                        Technologies, our customer service efficiency has
                        improved by 65%, and our customer satisfaction scores
                        have reached an all-time high. The team's expertise in
                        AI and natural language processing is exceptional, and
                        they delivered a solution that truly understands our
                        customers' needs.
                      </p>
                    </div>
                    <p class="text-sm text-gray-500 italic">
                      Project: AI Customer Service Chatbot
                    </p>
                  </div>

                  <div class="flex items-center">
                    <img
                      src="https://images.unsplash.com/photo-1580489944761-15a19d654956?auto=format&fit=crop&w=100&h=100&q=80"
                      alt="Emily Chen"
                      class="w-12 h-12 rounded-full object-cover mr-4"
                    />
                    <div>
                      <h4 class="font-semibold text-gray-900">Emily Chen</h4>
                      <p class="text-sm text-gray-500">
                        Customer Success Manager, ConnectTech
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Slider Controls -->
          <div class="flex justify-center items-center mt-8">
            <button
              id="prevTestimonial"
              class="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300 mr-4 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                ></path>
              </svg>
            </button>

            <div class="flex space-x-2">
              <button
                class="testimonial-dot w-3 h-3 rounded-full bg-primary-600"
              ></button>
              <button
                class="testimonial-dot w-3 h-3 rounded-full bg-gray-300"
              ></button>
              <button
                class="testimonial-dot w-3 h-3 rounded-full bg-gray-300"
              ></button>
              <button
                class="testimonial-dot w-3 h-3 rounded-full bg-gray-300"
              ></button>
            </div>

            <button
              id="nextTestimonial"
              class="w-10 h-10 rounded-full bg-white border border-gray-200 flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300 ml-4 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                ></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Client Success Stories -->
        <div data-aos="fade-up" data-aos-delay="300">
          <div class="text-center mb-12">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">
              Client Success Stories
            </h3>
            <p class="text-gray-600 max-w-3xl mx-auto">
              Explore how our solutions have helped businesses across various
              industries achieve remarkable results and overcome their digital
              challenges.
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Success Story 1 -->
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 group"
            >
              <div class="relative h-64">
                <img
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&w=800&q=80"
                  alt="E-commerce Success Story"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/80 to-transparent flex flex-col justify-end p-6"
                >
                  <span
                    class="px-3 py-1 rounded-full bg-white/90 backdrop-blur-sm text-xs font-medium text-primary-700 shadow-md inline-block w-fit mb-2"
                    >E-Commerce</span
                  >
                  <h4 class="text-xl font-bold text-white">
                    210% Increase in Conversion Rate
                  </h4>
                  <p class="text-white/90">LuxeMarket's Transformation Story</p>
                </div>
              </div>

              <div class="p-6">
                <div class="mb-4">
                  <p class="text-gray-600">
                    LuxeMarket faced declining online sales and high cart
                    abandonment rates. Our team implemented a complete redesign
                    with personalized shopping experiences, AR product previews,
                    and streamlined checkout process.
                  </p>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                  <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <p class="text-sm text-gray-500">Conversion Rate</p>
                    <p class="text-xl font-bold text-primary-600">+210%</p>
                  </div>
                  <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <p class="text-sm text-gray-500">Cart Abandonment</p>
                    <p class="text-xl font-bold text-primary-600">-45%</p>
                  </div>
                </div>

                <a
                  href="#"
                  class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                >
                  Read Full Case Study
                  <svg
                    class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M14 5l7 7m0 0l-7 7m7-7H3"
                    ></path>
                  </svg>
                </a>
              </div>
            </div>

            <!-- Success Story 2 -->
            <div
              class="bg-white rounded-2xl overflow-hidden shadow-soft hover:shadow-lg transition-all duration-300 group"
            >
              <div class="relative h-64">
                <img
                  src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?auto=format&fit=crop&w=800&q=80"
                  alt="Healthcare App Success Story"
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/80 to-transparent flex flex-col justify-end p-6"
                >
                  <span
                    class="px-3 py-1 rounded-full bg-white/90 backdrop-blur-sm text-xs font-medium text-primary-700 shadow-md inline-block w-fit mb-2"
                    >Healthcare</span
                  >
                  <h4 class="text-xl font-bold text-white">
                    65% Increase in Patient Engagement
                  </h4>
                  <p class="text-white/90">
                    HealthFirst's Digital Transformation
                  </p>
                </div>
              </div>

              <div class="p-6">
                <div class="mb-4">
                  <p class="text-gray-600">
                    HealthFirst Clinic needed a solution to improve patient
                    communication and monitoring. We developed a telemedicine
                    app with secure video consultations, appointment scheduling,
                    and continuous health monitoring.
                  </p>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                  <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <p class="text-sm text-gray-500">Patient Engagement</p>
                    <p class="text-xl font-bold text-primary-600">+65%</p>
                  </div>
                  <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <p class="text-sm text-gray-500">Administrative Time</p>
                    <p class="text-xl font-bold text-primary-600">-40%</p>
                  </div>
                </div>

                <a
                  href="#"
                  class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300"
                >
                  Read Full Case Study
                  <svg
                    class="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M14 5l7 7m0 0l-7 7m7-7H3"
                    ></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- JavaScript for Testimonial Slider -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const wrapper = document.getElementById("testimonialWrapper");
        const slides = document.querySelectorAll(".testimonial-slide");
        const dots = document.querySelectorAll(".testimonial-dot");
        const prevBtn = document.getElementById("prevTestimonial");
        const nextBtn = document.getElementById("nextTestimonial");
        let currentIndex = 0;
        const slidesToShow = window.innerWidth >= 768 ? 3 : 1;
        const slideWidth = 100 / slidesToShow;

        // Initialize slider
        function initSlider() {
          slides.forEach((slide) => {
            slide.style.minWidth = `${slideWidth}%`;
          });
          goToSlide(0);
        }

        // Go to specific slide
        function goToSlide(index) {
          if (index < 0) {
            index = slides.length - slidesToShow;
          } else if (index > slides.length - slidesToShow) {
            index = 0;
          }

          currentIndex = index;
          const translateValue = -index * slideWidth;
          wrapper.style.transform = `translateX(${translateValue}%)`;

          // Update dots
          dots.forEach((dot, i) => {
            dot.classList.remove("bg-primary-600");
            dot.classList.add("bg-gray-300");

            if (i === Math.floor(index / slidesToShow)) {
              dot.classList.remove("bg-gray-300");
              dot.classList.add("bg-primary-600");
            }
          });
        }

        // Event listeners
        prevBtn.addEventListener("click", () => {
          goToSlide(currentIndex - 1);
        });

        nextBtn.addEventListener("click", () => {
          goToSlide(currentIndex + 1);
        });

        dots.forEach((dot, i) => {
          dot.addEventListener("click", () => {
            goToSlide(i * slidesToShow);
          });
        });

        // Responsive adjustment
        window.addEventListener("resize", initSlider);

        // Initialize on load
        initSlider();

        // Auto slide
        setInterval(() => {
          goToSlide(currentIndex + 1);
        }, 5000);
      });
    </script>
    <!-- Portfolio Page - Modern Grid Layout with Interactive Features -->
    <section class="py-16 md:py-24 bg-white relative overflow-hidden">
      <!-- Background Elements -->
      <div class="absolute inset-0 z-0 overflow-hidden">
        <!-- Abstract shapes -->
        <div
          class="absolute top-0 right-0 w-72 h-72 bg-gradient-to-r from-primary-100 to-primary-200 rounded-full filter blur-3xl opacity-30 transform translate-x-1/4 -translate-y-1/4"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-72 h-72 bg-gradient-to-tr from-secondary-100 to-secondary-200 rounded-full filter blur-3xl opacity-30 transform -translate-x-1/4 translate-y-1/4"
        ></div>

        <!-- Grid pattern -->
        <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Section Header -->
        <div class="text-center mb-16" data-aos="fade-up">
          <span
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
          >
            Our Creative Work
          </span>
          <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            <span class="text-gradient">Showcasing</span> Our Digital Excellence
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our portfolio of successful projects where design meets
            functionality, creating meaningful digital experiences that drive
            results.
          </p>
        </div>

        <!-- Portfolio Filter Navigation -->
        <div
          class="flex flex-wrap justify-center mb-12 gap-3"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          <button
            class="filter-btn active px-5 py-2 rounded-full text-sm font-medium bg-primary-600 text-white hover:bg-primary-700 transition-all duration-300"
            data-filter="*"
          >
            All Projects
          </button>
          <button
            class="filter-btn px-5 py-2 rounded-full text-sm font-medium bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-700 border border-gray-200 transition-all duration-300"
            data-filter="web-design"
          >
            Web Design
          </button>
          <button
            class="filter-btn px-5 py-2 rounded-full text-sm font-medium bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-700 border border-gray-200 transition-all duration-300"
            data-filter="e-commerce"
          >
            E-Commerce
          </button>
          <button
            class="filter-btn px-5 py-2 rounded-full text-sm font-medium bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-700 border border-gray-200 transition-all duration-300"
            data-filter="branding"
          >
            Branding
          </button>
          <button
            class="filter-btn px-5 py-2 rounded-full text-sm font-medium bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-700 border border-gray-200 transition-all duration-300"
            data-filter="ui-ux"
          >
            UI/UX
          </button>
        </div>

        <!-- Portfolio Grid - Dynamically filled by JavaScript -->
        <div
          id="portfolio-grid"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <!-- Portfolio items will be inserted here by JavaScript -->
        </div>

        <!-- Load More Button -->
        <div
          class="flex justify-center mt-10"
          data-aos="fade-up"
          data-aos-delay="700"
        >
          <button
            id="load-more"
            class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-primary-700 bg-white border border-primary-200 hover:bg-primary-50 shadow-md hover:shadow-lg transform transition-all duration-300 hover:-translate-y-1"
          >
            <span>Load More Projects</span>
            <svg
              class="ml-2 w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              ></path>
            </svg>
          </button>
        </div>
      </div>
    </section>

    <!-- Project Details Modal -->
    <div
      id="project-modal"
      class="fixed inset-0 z-50 flex items-center justify-center hidden"
    >
      <div class="absolute inset-0 bg-black/70 backdrop-blur-sm"></div>
      <div
        class="relative bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto mx-4"
      >
        <button
          id="close-modal"
          class="absolute top-4 right-4 w-10 h-10 flex items-center justify-center rounded-full bg-white/10 text-gray-700 hover:bg-primary-50 hover:text-primary-700 transition-all duration-300 z-10"
        >
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>

        <div class="p-6 md:p-8">
          <div id="modal-content">
            <!-- Content will be dynamically inserted here by JavaScript -->
          </div>
        </div>
      </div>
    </div>

    <!-- Project Showcase Section with Interactive Slider -->
    <section class="py-20 bg-gray-50 relative overflow-hidden">
      <!-- Background Elements -->
      <div class="absolute inset-0 z-0">
        <div
          class="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-primary-100 to-primary-200 rounded-full filter blur-3xl opacity-30 transform -translate-x-1/4 translate-y-1/4"
        ></div>
        <div
          class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-full filter blur-3xl opacity-30 transform translate-x-1/4 -translate-y-1/4"
        ></div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16" data-aos="fade-up">
          <span
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
          >
            Featured Work
          </span>
          <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
            Our <span class="text-gradient">Signature</span> Projects
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our most impactful projects that showcase our expertise in
            creating beautiful, functional digital experiences.
          </p>
        </div>

        <!-- Featured Projects Slider -->
        <div
          class="featured-projects-slider relative overflow-hidden rounded-2xl shadow-lg"
          data-aos="fade-up"
          data-aos-delay="200"
        >
          <div
            id="slider-container"
            class="slider-container flex transition-transform duration-500 ease-in-out"
          >
            <!-- Slides will be inserted here by JavaScript -->
          </div>

          <!-- Slider Navigation -->
          <div class="absolute bottom-6 right-8 flex space-x-3 z-10">
            <button
              class="slider-prev w-10 h-10 rounded-full bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm transition-all duration-300 flex items-center justify-center"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                ></path>
              </svg>
            </button>
            <button
              class="slider-next w-10 h-10 rounded-full bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm transition-all duration-300 flex items-center justify-center"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                ></path>
              </svg>
            </button>
          </div>

          <!-- Slider Indicators -->
          <div
            id="slider-indicators"
            class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10"
          >
            <!-- Indicator dots will be inserted here by JavaScript -->
          </div>
        </div>
      </div>
    </section>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize AOS (Animate On Scroll)
        AOS.init({
          duration: 800,
          easing: "ease-in-out",
          once: true,
        });

        // Project Data - All our portfolio items
        const portfolioProjects = [
          {
            id: 1,
            title: "Ruby Hair Beauty",
            description: "Hair Salon & Beauty Services Website",
            categories: ["web-design", "branding"],
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Frubyhairbeauty.netlify.app&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://rubyhairbeauty.netlify.app",
            technologies: ["HTML5", "CSS3", "JavaScript", "Responsive"],
            delay: 100,
          },
          {
            id: 2,
            title: "Now Posh",
            description: "Fashion & Lifestyle Platform",
            categories: ["web-design", "ui-ux"],
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fnowposh.netlify.app&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://nowposh.netlify.app",
            technologies: ["React", "Tailwind CSS", "Netlify", "Responsive"],
            delay: 200,
          },
          {
            id: 3,
            title: "GreenServe",
            description: "Eco-Friendly Services Platform",
            categories: ["web-design", "branding"],
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fgreenserve-v1.netlify.app&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://greenserve-v1.netlify.app",
            technologies: ["NextJS", "SCSS", "Netlify", "API Integration"],
            delay: 300,
          },
          {
            id: 4,
            title: "Ever Last",
            description: "Sustainable Products E-commerce",
            categories: ["e-commerce", "web-design"],
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fa1-ever-last-v1.vercel.app&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://a1-ever-last-v1.vercel.app",
            technologies: ["Next.js", "Shopify", "Vercel", "Payment Gateway"],
            delay: 400,
          },
          {
            id: 5,
            title: "Dream Homes Developers",
            description: "Real Estate & Property Development",
            categories: ["web-design", "branding"],
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fwww.dreamhomesdevelopers.co.uk&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://www.dreamhomesdevelopers.co.uk",
            technologies: [
              "WordPress",
              "Custom Theme",
              "Property Listings",
              "CRM Integration",
            ],
            delay: 500,
          },
          {
            id: 6,
            title: "HFC Testing",
            description: "Testing Platform & Solutions",
            categories: ["web-design", "ui-ux"],
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fhfc-testing-1.vercel.app&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://hfc-testing-1.vercel.app",
            technologies: ["React", "Node.js", "Express", "MongoDB"],
            delay: 600,
          },
          {
            id: 7,
            title: "Hosting UI",
            description: "Web Hosting Interface Design",
            categories: ["web-design", "branding"],
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fhosting-ui.vercel.app%2F&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://hosting-ui.vercel.app/",
            technologies: ["React", "Material UI", "Hosting API", "Dashboard"],
            delay: 700,
          },
          {
            id: 8,
            title: "Mos Flaming Grill",
            description: "Restaurant & Food Services",
            categories: ["web-design", "ui-ux"],
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fwww.mosflaminggrill.co.uk&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://www.mosflaminggrill.co.uk",
            technologies: [
              "WordPress",
              "Online Ordering",
              "Menu System",
              "Restaurant CMS",
            ],
            delay: 800,
          },
        ];

        // Featured projects for the slider
        const featuredProjects = [
          {
            id: 101,
            title: "Ever Last Sustainable Shop",
            category: "E-Commerce",
            description:
              "A modern e-commerce platform focused on sustainable products with seamless shopping experience and personalized recommendations.",
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fa1-ever-last-v1.vercel.app&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://a1-ever-last-v1.vercel.app",
          },
          {
            id: 102,
            title: "Dream Homes Developers",
            category: "Real Estate",
            description:
              "An elegant property showcase platform with advanced search capabilities, virtual tours, and integrated CRM for real estate professionals.",
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fwww.dreamhomesdevelopers.co.uk&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://www.dreamhomesdevelopers.co.uk",
          },
          {
            id: 103,
            title: "Mos Flaming Grill",
            category: "Restaurant",
            description:
              "A mouth-watering restaurant website with online reservation system, interactive menu, and seamless food ordering capabilities.",
            imageUrl:
              "https://api.microlink.io/?url=https%3A%2F%2Fwww.mosflaminggrill.co.uk&screenshot=true&meta=false&embed=screenshot.url",
            projectUrl: "https://www.mosflaminggrill.co.uk",
          },
        ];

        // Function to create portfolio items
        function renderPortfolioItems(projects, container) {
          const portfolioGrid = document.getElementById(container);
          portfolioGrid.innerHTML = ""; // Clear existing content

          projects.forEach((project) => {
            const portfolioItem = document.createElement("div");
            portfolioItem.className = "portfolio-item group";
            portfolioItem.setAttribute(
              "data-category",
              project.categories.join(" ")
            );
            portfolioItem.setAttribute("data-aos", "fade-up");
            portfolioItem.setAttribute("data-aos-delay", project.delay);
            portfolioItem.setAttribute("data-id", project.id);

            // Technologies list as HTML
            const techHTML = project.technologies
              .map(
                (tech) =>
                  `<span class="text-xs px-2 py-1 bg-white/20 text-white rounded-full">${tech}</span>`
              )
              .join("");

            portfolioItem.innerHTML = `
                <div class="relative overflow-hidden rounded-xl shadow-soft transition-all duration-500 hover:shadow-lg">
                    <!-- Image Container with Hover Effect -->
                    <div class="relative overflow-hidden aspect-[4/3]">
                        <!-- Website Screenshot Using Microlink API -->
                        <img
                            src="${project.imageUrl}"
                            alt="${project.title}"
                            class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                            loading="lazy"
                        >

                        <!-- Overlay with Project Info -->
                        <div class="absolute inset-0 bg-gradient-to-t from-primary-900/90 to-primary-700/70 opacity-0 group-hover:opacity-100 transition-all duration-500 flex flex-col justify-end p-6">
                            <div class="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                                <h3 class="text-xl font-bold text-white mb-2">${project.title}</h3>
                                <p class="text-primary-100 mb-4">${project.description}</p>
                                <div class="flex flex-wrap gap-2 mb-4">
                                    ${techHTML}
                                </div>
                                <a href="${project.projectUrl}" target="_blank" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-full text-white bg-white/20 hover:bg-white/30 backdrop-blur-sm transition-all duration-300">
                                    <span>View Project</span>
                                    <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Project Info (Visible by default) -->
                    <div class="bg-white p-6 border-t border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors duration-300">${project.title}</h3>
                        <p class="text-gray-600">${project.description}</p>
                    </div>
                </div>
            `;

            portfolioGrid.appendChild(portfolioItem);

            // Add click handler for the portfolio item
            portfolioItem.addEventListener("click", function (e) {
              // If user clicked on the "View Project" link, don't open the modal
              if (e.target.closest('a[target="_blank"]')) {
                return;
              }
              openProjectModal(project);
            });
          });
        }

        // Function to create slider items
        function renderSliderItems(projects) {
          const sliderContainer = document.getElementById("slider-container");
          const sliderIndicators = document.getElementById("slider-indicators");
          sliderContainer.innerHTML = ""; // Clear existing content
          sliderIndicators.innerHTML = ""; // Clear existing indicators

          projects.forEach((project, index) => {
            // Create slide
            const slide = document.createElement("div");
            slide.className = "slider-slide min-w-full";
            slide.innerHTML = `
                <div class="relative aspect-video">
                    <img src="${project.imageUrl}" alt="${project.title}" class="w-full h-full object-cover">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent flex flex-col justify-end p-8 md:p-12">
                        <span class="text-sm font-medium text-primary-300 mb-2">${project.category}</span>
                        <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">${project.title}</h3>
                        <p class="text-gray-200 mb-6 max-w-2xl">${project.description}</p>
                        <a href="${project.projectUrl}" target="_blank" class="inline-flex items-center px-5 py-2.5 text-sm font-medium rounded-full text-white bg-primary-600 hover:bg-primary-700 transition-all duration-300 self-start">
                            <span>View Project</span>
                            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            `;
            sliderContainer.appendChild(slide);

            // Create indicator dot
            const dot = document.createElement("button");
            dot.className = `slider-dot w-2.5 h-2.5 rounded-full ${
              index === 0 ? "bg-white" : "bg-white/40"
            } hover:bg-white/60 transition-all duration-300`;
            dot.setAttribute("data-index", index);
            sliderIndicators.appendChild(dot);

            // Add click handler for the indicator
            dot.addEventListener("click", function () {
              currentSlideIndex = index;
              updateSlider();
            });
          });
        }

        // Function to open project modal
        function openProjectModal(project) {
          const modalContent = document.getElementById("modal-content");

          // Generate technologies badges
          const techBadges = project.technologies
            .map(
              (tech) =>
                `<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 mr-2 mb-2">${tech}</span>`
            )
            .join("");

          modalContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <img src="${project.imageUrl}" alt="${
            project.title
          }" class="rounded-xl shadow-md w-full h-auto">
                </div>
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">${
                      project.title
                    }</h2>
                    <p class="text-lg text-gray-600 mb-4">${
                      project.description
                    }</p>

                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Technologies Used</h3>
                        <div class="flex flex-wrap">
                            ${techBadges}
                        </div>
                    </div>

                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Project Overview</h3>
                        <p class="text-gray-600">
                            This project showcases our expertise in ${project.categories.join(
                              " and "
                            )} with a focus on creating
                            a seamless user experience while maintaining high performance standards.
                        </p>
                    </div>

                    <a href="${
                      project.projectUrl
                    }" target="_blank" class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-primary-600 hover:bg-primary-700 transition-all duration-300">
                        <span>Visit Live Site</span>
                        <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                    </a>
                </div>
            </div>
        `;

          document.getElementById("project-modal").classList.remove("hidden");
        }

        // Portfolio Filtering
        const filterBtns = document.querySelectorAll(".filter-btn");

        filterBtns.forEach((btn) => {
          btn.addEventListener("click", () => {
            // Remove active class from all buttons
            filterBtns.forEach((b) => {
              b.classList.remove("active", "bg-primary-600", "text-white");
              b.classList.add("bg-white", "text-gray-700");
            });

            // Add active class to clicked button
            btn.classList.add("active", "bg-primary-600", "text-white");
            btn.classList.remove("bg-white", "text-gray-700");

            const filterValue = btn.getAttribute("data-filter");

            // Filter the projects
            if (filterValue === "*") {
              // Show all projects
              renderPortfolioItems(portfolioProjects, "portfolio-grid");
            } else {
              // Filter projects by category
              const filteredProjects = portfolioProjects.filter((project) =>
                project.categories.includes(filterValue)
              );
              renderPortfolioItems(filteredProjects, "portfolio-grid");
            }
          });
        });

        // Slider functionality
        let currentSlideIndex = 0;
        const prevBtn = document.querySelector(".slider-prev");
        const nextBtn = document.querySelector(".slider-next");

        function updateSlider() {
          const sliderContainer = document.querySelector(".slider-container");
          sliderContainer.style.transform = `translateX(-${
            currentSlideIndex * 100
          }%)`;

          // Update indicators
          const dots = document.querySelectorAll(".slider-dot");
          dots.forEach((dot, index) => {
            dot.classList.toggle("bg-white", index === currentSlideIndex);
            dot.classList.toggle("bg-white/40", index !== currentSlideIndex);
          });
        }

        nextBtn.addEventListener("click", () => {
          currentSlideIndex = (currentSlideIndex + 1) % featuredProjects.length;
          updateSlider();
        });

        prevBtn.addEventListener("click", () => {
          currentSlideIndex =
            (currentSlideIndex - 1 + featuredProjects.length) %
            featuredProjects.length;
          updateSlider();
        });

        // Load More Projects Button
        const loadMoreBtn = document.getElementById("load-more");
        const initialProjectCount = 6; // Show first 6 projects initially
        let currentProjectCount = initialProjectCount;

        loadMoreBtn.addEventListener("click", () => {
          if (currentProjectCount >= portfolioProjects.length) {
            loadMoreBtn.innerHTML = "<span>All Projects Loaded</span>";
            loadMoreBtn.disabled = true;
            loadMoreBtn.classList.add("opacity-50", "cursor-not-allowed");
            return;
          }

          // Load 3 more projects
          currentProjectCount = Math.min(
            currentProjectCount + 3,
            portfolioProjects.length
          );

          // Re-render with more projects
          renderPortfolioItems(
            portfolioProjects.slice(0, currentProjectCount),
            "portfolio-grid"
          );

          // Disable button if all projects are loaded
          if (currentProjectCount >= portfolioProjects.length) {
            loadMoreBtn.innerHTML = "<span>All Projects Loaded</span>";
            loadMoreBtn.disabled = true;
            loadMoreBtn.classList.add("opacity-50", "cursor-not-allowed");
          }
        });

        // Modal close functionality
        const projectModal = document.getElementById("project-modal");
        const closeModalBtn = document.getElementById("close-modal");

        closeModalBtn.addEventListener("click", () => {
          projectModal.classList.add("hidden");
        });

        projectModal.addEventListener("click", (e) => {
          if (e.target === projectModal) {
            projectModal.classList.add("hidden");
          }
        });

        document.addEventListener("keydown", (e) => {
          if (
            e.key === "Escape" &&
            !projectModal.classList.contains("hidden")
          ) {
            projectModal.classList.add("hidden");
          }
        });

        // Initialize the portfolio and slider on page load
        renderPortfolioItems(
          portfolioProjects.slice(0, initialProjectCount),
          "portfolio-grid"
        );
        renderSliderItems(featuredProjects);
      });
    </script>

    <style>
      /* Grid pattern for background */
      .bg-grid-pattern {
        background-image: linear-gradient(
            to right,
            rgba(107, 70, 193, 0.05) 1px,
            transparent 1px
          ),
          linear-gradient(
            to bottom,
            rgba(107, 70, 193, 0.05) 1px,
            transparent 1px
          );
        background-size: 40px 40px;
      }

      /* Active filter button styles */
      .filter-btn.active {
        @apply bg-primary-600 text-white;
      }

      /* Portfolio item transition */
      .portfolio-item {
        transition: opacity 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
      }
    </style>
    <!-- CTA and Contact Section -->
    <section id="contact" class="py-20 relative overflow-hidden">
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blobs -->
        <div
          class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-primary-300/30 to-primary-600/30 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-secondary-300/30 to-secondary-600/30 rounded-full filter blur-3xl opacity-30 animate-float"
          style="animation-delay: 1.5s"
        ></div>

        <!-- SVG Pattern Background -->
        <div class="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <pattern
              id="contact-dots"
              x="0"
              y="0"
              width="30"
              height="30"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <circle
                id="contact-dot"
                cx="15"
                cy="15"
                r="1"
                fill="#6d28d9"
              ></circle>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#contact-dots)"
            ></rect>
          </svg>
        </div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Call to Action Section -->
        <div class="relative mb-24" data-aos="fade-up">
          <div
            class="bg-gradient-purple-pink rounded-3xl overflow-hidden shadow-xl"
          >
            <div class="relative p-8 md:p-12 lg:p-16">
              <!-- Background Decorations -->
              <div
                class="absolute top-0 right-0 w-64 h-64 bg-white rounded-full opacity-10 transform translate-x-1/3 -translate-y-1/3"
              ></div>
              <div
                class="absolute bottom-0 left-0 w-64 h-64 bg-white rounded-full opacity-10 transform -translate-x-1/3 translate-y-1/3"
              ></div>

              <!-- Abstract SVG Shape -->
              <svg
                class="absolute bottom-0 right-0 w-80 h-80 text-white opacity-10"
                viewBox="0 0 200 200"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill="currentColor"
                  d="M44.3,-76.5C58.4,-71.2,71.3,-61.3,79.4,-48.1C87.5,-34.8,90.8,-18.4,90.3,-2.4C89.8,13.5,85.6,27.1,77.2,38.2C68.9,49.3,56.3,58,43.1,65.5C29.9,73,14.9,79.4,0.3,78.9C-14.3,78.5,-28.7,71.3,-42.7,63.3C-56.7,55.3,-70.4,46.4,-78.8,33.5C-87.2,20.6,-90.3,3.6,-87.4,-12.1C-84.5,-27.8,-75.5,-42.2,-63.9,-53.4C-52.3,-64.5,-38.1,-72.3,-23.6,-76.9C-9.1,-81.5,5.7,-82.9,19.4,-80.8C33.1,-78.7,45.7,-73.2,44.3,-76.5Z"
                  transform="translate(100 100)"
                />
              </svg>

              <div class="grid grid-cols-1 lg:grid-cols-5 gap-10 items-center">
                <div class="lg:col-span-3">
                  <h2
                    class="text-3xl md:text-4xl font-extrabold text-white mb-6"
                  >
                    Ready to Transform Your Digital Presence?
                  </h2>
                  <p class="text-white/90 text-xl mb-8 max-w-2xl">
                    Partner with RayDesign Technologies to create impactful
                    digital experiences that drive growth, enhance user
                    engagement, and boost your business results.
                  </p>

                  <div class="space-y-4 mb-8">
                    <div class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 mt-1 bg-white/20 rounded-full flex items-center justify-center"
                      >
                        <svg
                          class="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </div>
                      <p class="ml-3 text-white/90">
                        Expert team with proven track record across industries
                      </p>
                    </div>
                    <div class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 mt-1 bg-white/20 rounded-full flex items-center justify-center"
                      >
                        <svg
                          class="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </div>
                      <p class="ml-3 text-white/90">
                        Innovative solutions that keep you ahead of the
                        competition
                      </p>
                    </div>
                    <div class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 mt-1 bg-white/20 rounded-full flex items-center justify-center"
                      >
                        <svg
                          class="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </div>
                      <p class="ml-3 text-white/90">
                        Comprehensive solutions that grow with your business
                      </p>
                    </div>
                  </div>

                  <div class="flex flex-wrap gap-4">
                    <a
                      href="#contactForm"
                      class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-primary-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                    >
                      <span>Get Started Today</span>
                      <svg
                        class="ml-2 -mr-1 w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                          clip-rule="evenodd"
                        ></path>
                      </svg>
                    </a>
                    <a
                      href="tel:+447878361409"
                      class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white border border-white/30 backdrop-blur-sm hover:bg-white/10 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                    >
                      <svg
                        class="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
                        ></path>
                      </svg>
                      <span>Call Us Now</span>
                    </a>
                  </div>
                </div>

                <div class="lg:col-span-2">
                  <div
                    class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20"
                  >
                    <h3 class="text-xl font-bold text-white mb-4">
                      Get a Free Consultation
                    </h3>
                    <p class="text-white/80 mb-6">
                      Schedule a 45-minute consultation with our experts to
                      discuss your project needs.
                    </p>

                    <form
                      action="https://submit-form.com/l2ArscGWs"
                      class="space-y-4"
                    >
                      <div>
                        <label for="quick-name" class="sr-only"
                          >Your Name</label
                        >
                        <input
                          type="text"
                          id="quick-name"
                          name="quick-name"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                          placeholder="Your Name"
                        />
                      </div>
                      <div>
                        <label for="quick-email" class="sr-only"
                          >Your Email</label
                        >
                        <input
                          type="email"
                          id="quick-email"
                          name="quick-email"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                          placeholder="Your Email"
                        />
                      </div>
                      <div>
                        <label for="quick-service" class="sr-only"
                          >Service Interested In</label
                        >
                        <select
                          id="quick-service"
                          name="quick-service"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                        >
                          <option
                            value=""
                            disabled
                            selected
                            class="text-gray-500"
                          >
                            Service Interested In
                          </option>
                          <option value="web-development" class="text-gray-800">
                            Web Development
                          </option>
                          <option value="mobile-app" class="text-gray-800">
                            Mobile App Development
                          </option>
                          <option value="ui-ux" class="text-gray-800">
                            UI/UX Design
                          </option>
                          <option
                            value="digital-marketing"
                            class="text-gray-800"
                          >
                            Digital Marketing
                          </option>
                          <option value="ai-solutions" class="text-gray-800">
                            AI Solutions
                          </option>
                          <option value="other" class="text-gray-800">
                            Other Services
                          </option>
                        </select>
                      </div>

                      <button
                        type="submit"
                        class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium rounded-lg text-primary-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                      >
                        Book My Free Consultation
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Wave Separator -->
          <svg
            class="absolute -bottom-1 left-0 w-full text-white"
            viewBox="0 0 1440 96"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0,96L60,80C120,64,240,32,360,32C480,32,600,64,720,64C840,64,960,32,1080,24C1200,16,1320,32,1380,40L1440,48L1440,96L1380,96C1320,96,1200,96,1080,96C960,96,840,96,720,96C600,96,480,96,360,96C240,96,120,96,60,96L0,96Z"
              fill="currentColor"
            ></path>
          </svg>
        </div>

        <!-- Contact Us Section -->
        <div
          id="contactForm"
          class="mt-16"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          <div class="text-center max-w-3xl mx-auto mb-16">
            <span
              class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
            >
              Get In Touch
            </span>
            <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">
              Let's Discuss Your <span class="text-gradient">Project</span>
            </h2>
            <p class="text-xl text-gray-600">
              Fill out the form below, and our team will get back to you within
              24 hours to discuss how we can help bring your vision to life.
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
            <!-- Contact Form -->
            <div class="lg:col-span-2">
              <div
                class="bg-white rounded-2xl shadow-soft p-8 border border-gray-100"
              >
                <form
                  action="https://submit-form.com/l2ArscGWs"
                  class="space-y-6"
                >
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label
                        for="name"
                        class="block text-sm font-medium text-gray-700 mb-1"
                        >Your Name</label
                      >
                      <input
                        type="text"
                        id="name"
                        name="name"
                        class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                        placeholder="John Smith"
                      />
                    </div>
                    <div>
                      <label
                        for="email"
                        class="block text-sm font-medium text-gray-700 mb-1"
                        >Email Address</label
                      >
                      <input
                        type="email"
                        id="email"
                        name="email"
                        class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label
                        for="phone"
                        class="block text-sm font-medium text-gray-700 mb-1"
                        >Phone Number</label
                      >
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                        placeholder="+44 7700 900000"
                      />
                    </div>
                    <div>
                      <label
                        for="company"
                        class="block text-sm font-medium text-gray-700 mb-1"
                        >Company Name</label
                      >
                      <input
                        type="text"
                        id="company"
                        name="company"
                        class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                        placeholder="Your Company Ltd"
                      />
                    </div>
                  </div>

                  <div>
                    <label
                      for="service"
                      class="block text-sm font-medium text-gray-700 mb-1"
                      >Service You're Interested In</label
                    >
                    <select
                      id="service"
                      name="service"
                      class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                    >
                      <option value="" disabled selected>
                        Select a Service
                      </option>
                      <option value="web-development">
                        Web Design & Development
                      </option>
                      <option value="mobile-app">Mobile App Development</option>
                      <option value="ecommerce">E-Commerce Solutions</option>
                      <option value="ui-ux">UI/UX Design</option>
                      <option value="branding">Branding & Identity</option>
                      <option value="digital-marketing">
                        Digital Marketing
                      </option>
                      <option value="seo">SEO Services</option>
                      <option value="ppc">PPC Advertising</option>
                      <option value="social-media">
                        Social Media Management
                      </option>
                      <option value="ai-solutions">
                        AI & Automation Solutions
                      </option>
                      <option value="other">Other Services</option>
                    </select>
                  </div>

                  <div>
                    <label
                      for="budget"
                      class="block text-sm font-medium text-gray-700 mb-1"
                      >Project Budget</label
                    >
                    <select
                      id="budget"
                      name="budget"
                      class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                    >
                      <option value="" disabled selected>
                        Select Budget Range
                      </option>
                      <option value="less-5k">Less than £5,000</option>
                      <option value="5k-10k">£5,000 - £10,000</option>
                      <option value="10k-25k">£10,000 - £25,000</option>
                      <option value="25k-50k">£25,000 - £50,000</option>
                      <option value="50k-100k">£50,000 - £100,000</option>
                      <option value="more-100k">More than £100,000</option>
                      <option value="not-sure">Not sure yet</option>
                    </select>
                  </div>

                  <div>
                    <label
                      for="message"
                      class="block text-sm font-medium text-gray-700 mb-1"
                      >Project Details</label
                    >
                    <textarea
                      id="message"
                      name="message"
                      rows="5"
                      class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                      placeholder="Tell us about your project, goals, and requirements..."
                    ></textarea>
                  </div>

                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="terms"
                        name="terms"
                        type="checkbox"
                        class="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="terms" class="text-gray-600"
                        >I agree to the
                        <a
                          href="#"
                          class="text-primary-600 hover:text-primary-700"
                          >Privacy Policy</a
                        >
                        and
                        <a
                          href="#"
                          class="text-primary-600 hover:text-primary-700"
                          >Terms of Service</a
                        ></label
                      >
                    </div>
                  </div>

                  <div>
                    <button
                      type="submit"
                      class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium rounded-lg text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <span>Submit Inquiry</span>
                      <svg
                        class="ml-2 -mr-1 w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                          clip-rule="evenodd"
                        ></path>
                      </svg>
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <!-- Contact Information -->
            <div>
              <div
                class="bg-white rounded-2xl shadow-soft p-8 border border-gray-100 mb-8"
              >
                <h3 class="text-xl font-bold text-gray-900 mb-6">
                  Contact Information
                </h3>

                <div class="space-y-6">
                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-primary-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-semibold text-gray-900">
                        Phone
                      </h4>
                      <p class="text-gray-600 mt-1">
                        UK:
                        <a
                          href="tel:+447878361409"
                          class="text-primary-600 hover:text-primary-700"
                          >+44 7878 361409</a
                        >
                      </p>
                      <!-- <p class="text-gray-600">
                   
                        <a
                          href="tel:+17862337886"
                          class="text-primary-600 hover:text-primary-700"
                        >
                      </p> -->
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-primary-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-semibold text-gray-900">
                        Email
                      </h4>
                      <p class="text-gray-600 mt-1">
                        General Inquiries:
                        <a
                          href="mailto:<EMAIL>"
                          class="text-primary-600 hover:text-primary-700"
                          ><EMAIL></a
                        >
                      </p>
                      <p class="text-gray-600">
                        Support Requests:
                        <a
                          href="mailto:<EMAIL>"
                          class="text-primary-600 hover:text-primary-700"
                          ><EMAIL></a
                        >
                      </p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-primary-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                          ></path>
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-semibold text-gray-900">
                        Address
                      </h4>
                      <p class="text-gray-600 mt-1">1-A Edmundson Street,</p>
                      <p class="text-gray-600">Blackburn BB2 1HL,</p>
                      <p class="text-gray-600">United Kingdom</p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-primary-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-semibold text-gray-900">
                        Business Hours
                      </h4>
                      <p class="text-gray-600 mt-1">
                        Monday - Friday: 08:30 AM - 06:00 PM (GMT)
                      </p>
                      <p class="text-gray-600">
                        Emergency Support Available 24/7
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Social Media -->
              <div
                class="bg-white rounded-2xl shadow-soft p-8 border border-gray-100"
              >
                <h3 class="text-xl font-bold text-gray-900 mb-6">
                  Connect With Us
                </h3>

                <div class="grid grid-cols-4 gap-4">
                  <a
                    href="#"
                    class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group"
                  >
                    <i
                      class="fab fa-linkedin text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                    ></i>
                    <span
                      class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                      >LinkedIn</span
                    >
                  </a>
                  <a
                    href="#"
                    class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group"
                  >
                    <i
                      class="fab fa-twitter text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                    ></i>
                    <span
                      class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                      >Twitter</span
                    >
                  </a>
                  <a
                    href="#"
                    class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group"
                  >
                    <i
                      class="fab fa-facebook text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                    ></i>
                    <span
                      class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                      >Facebook</span
                    >
                  </a>
                  <a
                    href="#"
                    class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group"
                  >
                    <i
                      class="fab fa-instagram text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                    ></i>
                    <span
                      class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                      >Instagram</span
                    >
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Google Map -->
    <div class="w-full h-96 relative mt-16">
      <iframe
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2362.1054555573354!2d-2.4908899842347944!3d53.748943980074004!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x487b9f6cf9e71a1d%3A0x7f0e9f7a79eb2b31!2sEdmundson%20St%2C%20Blackburn%20BB2%201HL%2C%20UK!5e0!3m2!1sen!2sus!4v1631289726754!5m2!1sen!2sus"
        width="100%"
        height="100%"
        style="border: 0"
        allowfullscreen=""
        loading="lazy"
        class="absolute top-0 left-0 w-full h-full z-0"
      ></iframe>

      <div
        class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 w-full max-w-md px-4"
      >
        <div class="bg-white rounded-xl shadow-xl p-6 backdrop-blur-md">
          <div class="flex items-center">
            <div
              class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center flex-shrink-0"
            >
              <svg
                class="w-6 h-6 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                ></path>
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                ></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">
                RayDesign Technologies
              </h3>
              <p class="text-gray-600 text-sm">
                1-A Edmundson Street, Blackburn BB2 1HL, United Kingdom
              </p>
            </div>
          </div>
          <div class="mt-4 flex space-x-2">
            <a
              href="https://www.google.com/maps/dir//Edmundson+St,+Blackburn+BB2+1HL,+UK/"
              target="_blank"
              class="flex-1 inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-300"
            >
              <svg
                class="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                ></path>
              </svg>
              Get Directions
            </a>
            <a
              href="tel:+447878361409"
              class="inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-lg text-primary-600 bg-primary-50 hover:bg-primary-100 transition-colors duration-300"
            >
              <svg
                class="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                ></path>
              </svg>
              Call
            </a>
          </div>
        </div>
      </div>
    </div>
    <!-- Footer Section with Comprehensive Information -->
    <footer
      class="bg-gradient-to-b from-gray-50 to-gray-100 pt-20 pb-10 relative overflow-hidden"
    >
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blobs -->
        <div
          class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-primary-300/10 to-primary-600/10 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"
        ></div>
        <div
          class="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-tr from-secondary-300/10 to-secondary-600/10 rounded-full filter blur-3xl opacity-30 animate-float"
          style="animation-delay: 2s"
        ></div>

        <!-- SVG Pattern Background -->
        <div class="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <pattern
              id="footer-dots"
              x="0"
              y="0"
              width="30"
              height="30"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <circle
                id="footer-dot"
                cx="15"
                cy="15"
                r="1"
                fill="#6d28d9"
              ></circle>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#footer-dots)"
            ></rect>
          </svg>
        </div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Footer Top Section -->
        <div
          class="grid grid-cols-1 lg:grid-cols-5 gap-10 pb-10 border-b border-gray-200"
        >
          <!-- Company Information -->
          <div class="lg:col-span-2">
            <a href="/" class="inline-block mb-6">
              <img
                class="h-16 w-auto"
                src="https://i.postimg.cc/cCwXxx3N/Ray-Design-Logo.png"
                alt="RayDesign Technologies Logo"
              />
            </a>
            <p class="text-gray-600 mb-6 max-w-md">
              RayDesign Technologies is a future-forging innovation powerhouse
              that merges design thinking, intelligent automation, full-stack
              engineering, and marketing science to architect high-impact,
              human-centered digital ecosystems.
            </p>
            <div class="flex space-x-4 mb-6">
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-facebook-f"></i>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-twitter"></i>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-linkedin-in"></i>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-instagram"></i>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-youtube"></i>
              </a>
            </div>
            <div class="bg-white rounded-xl shadow-sm p-4 flex items-center">
              <div class="flex-shrink-0 mr-4">
                <div
                  class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-primary-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div>
                <p class="text-sm text-gray-500">
                  Have a question? Call us now
                </p>
                <a
                  href="tel:+447878361409"
                  class="text-lg font-semibold text-primary-600 hover:text-primary-700"
                  >+44 7878 361409</a
                >
              </div>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-lg font-bold text-gray-900 mb-6">Quick Links</h3>
            <ul class="space-y-3">
              <li>
                <a
                  href="/"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Home
                </a>
              </li>
              <li>
                <a
                  href="/about.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  About Us
                </a>
              </li>
              <li></li>
              <li>
                <a
                  href="portfolio.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Portfolio
                </a>
              </li>

              <li>
                <a
                  href="contact.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Contact Us
                </a>
              </li>
              <li>
                <a
                  href=""
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Careers
                </a>
              </li>
            </ul>
          </div>

          <!-- Services -->
          <div>
            <h3 class="text-lg font-bold text-gray-900 mb-6">Our Services</h3>
            <ul class="space-y-3">
              <li>
                <a
                  href="/services/ai-solutions.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  AI & Automation
                </a>
              </li>
              <li>
                <a
                  href="services/branding.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Branding & Identity
                </a>
              </li>
              <li>
                <a
                  href="services/e-commerce.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  E-Commerce Solutions
                </a>
              </li>
              <li>
                <a
                  href="/services/mobile-apps.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Mobile App Development
                </a>
              </li>
              <li>
                <a
                  href="/services/ppc.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Pay-Per-Click (PPC) Ads
                </a>
              </li>
              <li>
                <a
                  href="/services/seo.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  SEO Services
                </a>
              </li>
              <li>
                <a
                  href="/services/social-media.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Social Media Management
                </a>
              </li>
              <li>
                <a
                  href="/services/ui-ux-design.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  UI/UX Design
                </a>
              </li>
              <li>
                <a
                  href="/services/web-development.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Web Development
                </a>
              </li>
            </ul>
          </div>

          <!-- Newsletter Signup -->
          <div>
            <h3 class="text-lg font-bold text-gray-900 mb-6">
              Subscribe to Our Newsletter
            </h3>
            <p class="text-gray-600 mb-4">
              Stay updated with our latest news, tips, and special offers.
            </p>
            <form action="https://submit-form.com/l2ArscGWs" class="space-y-3">
              <div>
                <label for="footer-email" class="sr-only">Email Address</label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <svg
                      class="w-5 h-5 text-gray-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
                      ></path>
                      <path
                        d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
                      ></path>
                    </svg>
                  </div>
                  <input
                    type="email"
                    id="footer-email"
                    name="email"
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white"
                    placeholder="Your email address"
                  />
                </div>
              </div>
              <button
                type="submit"
                class="w-full inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-lg text-white bg-gradient-purple shadow-sm hover:shadow-md transform transition-all duration-300 hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"
                  ></path>
                </svg>
                Subscribe
              </button>
            </form>

            <!-- Industry Badges -->
            <div class="mt-6">
              <p class="text-sm text-gray-500 mb-3">Recognized By:</p>
              <div class="flex flex-wrap gap-3">
                <p>Google Verified Business</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer Middle Section - Industries We Serve -->
        <div class="py-10 border-b border-gray-200">
          <h3 class="text-lg font-bold text-gray-900 mb-6">
            Industries We Serve
          </h3>
          <div
            class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8 gap-4"
          >
            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Retail & eCommerce</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Healthcare</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Finance & Fintech</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Education</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Real Estate</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Travel & Hospitality</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"
                  ></path>
                  <path
                    d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707l-2-2A1 1 0 0015 7h-1z"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Logistics</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Legal & Compliance</span
              >
            </a>
          </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="pt-10">
          <div
            class="flex flex-col md:flex-row md:justify-between md:items-center"
          >
            <div class="mb-6 md:mb-0">
              <p class="text-sm text-gray-500">
                &copy; 2023 RayDesign Technologies. All rights reserved.
              </p>
            </div>

            <div class="flex flex-col md:flex-row gap-4 md:gap-8">
              <a
                href="privacy-policy.html"
                class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300"
                >Privacy Policy</a
              >
              <a
                href="terms-of-service.html"
                class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300"
                >Terms of Service</a
              >
              <a
                href="cookie-policy.html"
                class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300"
                >Cookie Policy</a
              >
              <a
                href="sitemap.html"
                class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300"
                >Sitemap</a
              >
            </div>
          </div>

          <div
            class="mt-6 flex flex-col md:flex-row md:justify-between md:items-center"
          >
            <div class="mb-4 md:mb-0">
              <p class="text-xs text-gray-400">
                RayDesign Technologies is registered in the United Kingdom.
                Company No: 12345678
              </p>
            </div>

            <div>
              <p class="text-xs text-gray-400">
                Designed and developed with
                <span class="text-red-500">❤</span> by RayDesign Technologies
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Back to Top Button -->
    <button
      id="backToTop"
      class="fixed bottom-6 right-6 w-12 h-12 rounded-full bg-primary-600 text-white shadow-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 flex items-center justify-center transform scale-0 opacity-0"
    >
      <svg
        class="w-6 h-6"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 10l7-7m0 0l7 7m-7-7v18"
        ></path>
      </svg>
    </button>

    <!-- Cookie Consent -->
    <div
      id="cookieConsent"
      class="fixed bottom-0 left-0 w-full bg-white shadow-lg transform translate-y-full transition-transform duration-500 z-50"
    >
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div
          class="flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div class="mb-4 md:mb-0 md:mr-8">
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-0.5">
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"
                  ></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-lg font-medium text-gray-900">Cookie Notice</h3>
                <p class="mt-1 text-sm text-gray-600">
                  We use cookies to enhance your browsing experience, analyze
                  site traffic, and personalize content. By clicking "Accept All
                  Cookies", you agree to the storing of cookies on your device.
                </p>
                <div class="mt-2">
                  <a
                    href="cookie-policy.html"
                    class="text-sm font-medium text-primary-600 hover:text-primary-700"
                    >Learn more about our Cookie Policy</a
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-col sm:flex-row gap-3">
            <button
              id="cookieSettings"
              class="inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cookie Settings
            </button>
            <button
              id="acceptCookies"
              class="inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Accept All Cookies
            </button>
            <button
              id="declineCookies"
              class="inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Decline Non-Essential
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Initialize AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Main JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize AOS
        AOS.init({
          duration: 800,
          easing: "ease-in-out",
          once: true,
          mirror: false,
        });

        // Back to Top Button
        const backToTopButton = document.getElementById("backToTop");

        window.addEventListener("scroll", () => {
          if (window.pageYOffset > 300) {
            backToTopButton.classList.remove("scale-0", "opacity-0");
            backToTopButton.classList.add("scale-100", "opacity-100");
          } else {
            backToTopButton.classList.remove("scale-100", "opacity-100");
            backToTopButton.classList.add("scale-0", "opacity-0");
          }
        });

        backToTopButton.addEventListener("click", () => {
          window.scrollTo({
            top: 0,
            behavior: "smooth",
          });
        });

        // Cookie Consent
        const cookieConsent = document.getElementById("cookieConsent");
        const acceptCookies = document.getElementById("acceptCookies");
        const declineCookies = document.getElementById("declineCookies");
        const cookieSettings = document.getElementById("cookieSettings");

        // Check if user has already made a cookie choice
        const cookieChoice = localStorage.getItem("cookieChoice");

        if (!cookieChoice) {
          // Show cookie consent after 2 seconds
          setTimeout(() => {
            cookieConsent.classList.remove("translate-y-full");
            cookieConsent.classList.add("translate-y-0");
          }, 2000);
        }

        acceptCookies.addEventListener("click", () => {
          localStorage.setItem("cookieChoice", "accepted");
          cookieConsent.classList.remove("translate-y-0");
          cookieConsent.classList.add("translate-y-full");
        });

        declineCookies.addEventListener("click", () => {
          localStorage.setItem("cookieChoice", "declined");
          cookieConsent.classList.remove("translate-y-0");
          cookieConsent.classList.add("translate-y-full");
        });

        cookieSettings.addEventListener("click", () => {
          // Implement cookie settings modal or redirect to cookie policy page
          window.location.href = "cookie-policy.html";
        });
      });
    </script>
    <!-- JavaScript for Animated Counters -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const counters = document.querySelectorAll(".counter");
        const speed = 200;

        const animateCounter = (counter) => {
          const target = +counter.dataset.target;
          let count = 0;
          const inc = target / speed;

          const updateCount = () => {
            if (count < target) {
              count += inc;
              counter.innerText = Math.ceil(count);
              setTimeout(updateCount, 1);
            } else {
              counter.innerText = target;
            }
          };

          updateCount();
        };

        // Use Intersection Observer to trigger counter animation when visible
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                const counter = entry.target;
                animateCounter(counter);
                observer.unobserve(counter);
              }
            });
          },
          { threshold: 0.2 }
        );

        counters.forEach((counter) => {
          observer.observe(counter);
        });
      });
    </script>
    <!-- JavaScript for Services Section -->
    <script>
      // Filter functionality for service cards
      document.addEventListener("DOMContentLoaded", function () {
        const filterButtons = document.querySelectorAll(".service-filter-btn");
        const serviceCards = document.querySelectorAll(".service-card");

        filterButtons.forEach((button) => {
          button.addEventListener("click", () => {
            // Remove active class from all buttons
            filterButtons.forEach((btn) => {
              btn.classList.remove("active", "bg-primary-600", "text-white");
              btn.classList.add("bg-white", "text-gray-700");
            });

            // Add active class to clicked button
            button.classList.add("active", "bg-primary-600", "text-white");
            button.classList.remove("bg-white", "text-gray-700");

            const filter = button.getAttribute("data-filter");

            // Show/hide cards based on filter
            serviceCards.forEach((card) => {
              if (
                filter === "all" ||
                card.getAttribute("data-category") === filter
              ) {
                card.style.display = "block";
              } else {
                card.style.display = "none";
              }
            });
          });
        });
      });
      // Create cursor element
      const cursor = document.createElement("div");
      cursor.classList.add("custom-cursor");
      document.body.appendChild(cursor);

      // Add styles
      const style = document.createElement("style");
      style.innerHTML = `
  body {
    cursor: none;
  }

  .custom-cursor {
    position: fixed;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    pointer-events: none;
    transform: translate(-50%, -50%);
    z-index: 9999;

    /* Blue-Cyan-Purple Gradient */
    background: linear-gradient(
      135deg,
      rgba(0, 255, 255, 0.9) 0%,
      rgba(0, 128, 255, 0.9) 30%,
      rgba(128, 0, 255, 0.9) 60%,
      rgba(255, 0, 255, 0.9) 100%
    );

    box-shadow:
      0 0 6px rgba(0, 255, 255, 0.7),
      0 0 12px rgba(0, 128, 255, 0.6),
      0 0 20px rgba(128, 0, 255, 0.8);

    transition: all 0.2s ease;
    animation: rainbow-rotate 5s linear infinite, pulse-fade 2s ease-in-out infinite;
  }

  @keyframes rainbow-rotate {
    0% {
      filter: hue-rotate(0deg);
    }
    100% {
      filter: hue-rotate(360deg);
    }
  }

  @keyframes pulse-fade {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.08);
      opacity: 0.85;
    }
  }

  .custom-cursor.hollow {
    width: 35px;
    height: 35px;
    background: transparent;
    border: 2px solid rgba(0, 200, 255, 0.8);
    box-shadow:
      0 0 10px rgba(0, 128, 255, 0.7),
      inset 0 0 10px rgba(128, 0, 255, 0.5);
    animation: pulse 1.5s ease-in-out infinite alternate;
  }

  @keyframes pulse {
    0% {
      box-shadow:
        0 0 10px rgba(0, 128, 255, 0.7),
        inset 0 0 10px rgba(128, 0, 255, 0.5);
    }
    100% {
      box-shadow:
        0 0 15px rgba(0, 200, 255, 0.8),
        inset 0 0 15px rgba(181, 0, 255, 0.6);
    }
  }
`;

      document.head.appendChild(style);

      // Track mouse movement with light smoothing
      let mouseX = 0,
        mouseY = 0;
      let cursorX = 0,
        cursorY = 0;
      const smoothing = 0.2;

      document.addEventListener("mousemove", (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
      });

      // Check proximity to headings
      function checkHeadingProximity() {
        const headings = document.querySelectorAll("h1, h2, h3, h4, h5, h6");
        let nearHeading = false;

        headings.forEach((heading) => {
          const rect = heading.getBoundingClientRect();

          // Create a slightly larger detection area around the heading
          const expandedRect = {
            left: rect.left - 10,
            right: rect.right + 10,
            top: rect.top - 10,
            bottom: rect.bottom + 10,
          };

          // Check if cursor is near the heading
          if (
            mouseX >= expandedRect.left &&
            mouseX <= expandedRect.right &&
            mouseY >= expandedRect.top &&
            mouseY <= expandedRect.bottom
          ) {
            nearHeading = true;
          }
        });

        // Update cursor appearance based on proximity
        if (nearHeading) {
          cursor.classList.add("hollow");
        } else {
          cursor.classList.remove("hollow");
        }
      }

      // Animation loop for smooth movement and heading detection
      function animate() {
        // Smooth cursor movement
        cursorX += (mouseX - cursorX) * smoothing;
        cursorY += (mouseY - cursorY) * smoothing;

        // Update cursor position
        cursor.style.left = `${cursorX}px`;
        cursor.style.top = `${cursorY}px`;

        // Check if near headings
        checkHeadingProximity();

        // Continue animation
        requestAnimationFrame(animate);
      }

      // Start animation
      animate();

      // Hide default cursor when page loads
      document.addEventListener("DOMContentLoaded", () => {
        setTimeout(() => {
          document.body.style.cursor = "none";
        }, 100);
      });
    </script>
    <script>
      (function () {
        if (!window.chatbase || window.chatbase("getState") !== "initialized") {
          window.chatbase = (...arguments) => {
            if (!window.chatbase.q) {
              window.chatbase.q = [];
            }
            window.chatbase.q.push(arguments);
          };
          window.chatbase = new Proxy(window.chatbase, {
            get(target, prop) {
              if (prop === "q") {
                return target.q;
              }
              return (...args) => target(prop, ...args);
            },
          });
        }
        const onLoad = function () {
          const script = document.createElement("script");
          script.src = "https://www.chatbase.co/embed.min.js";
          script.id = "zo0iKR7_GZSw0On-rhGft";
          script.domain = "www.chatbase.co";
          document.body.appendChild(script);
        };
        if (document.readyState === "complete") {
          onLoad();
        } else {
          window.addEventListener("load", onLoad);
        }
      })();
    </script>
  </body>
</html>
