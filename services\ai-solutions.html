<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RayDesign Technologies | Elevating Businesses Through Digital Brilliance</title>
    <meta name="description" content="RayDesign Technologies - Your strategic transformation partner merging design thinking, intelligent automation, full-stack engineering, and marketing science to create high-impact digital ecosystems.">
    <meta name="keywords" content="web development, mobile app, UI/UX design, digital marketing, AI solutions, RPA, automation, branding, SEO, PPC, eCommerce, SaaS development">
    <meta name="author" content="RayDesign Technologies">

    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="RayDesign Technologies | Digital Innovation Partner">
    <meta property="og:description" content="Transforming business challenges into seamless digital experiences through innovative technology solutions and strategic digital marketing.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://raydesigntechnologies.com">
    <meta property="og:image" content="https://raydesigntechnologies.com/images/og-image.jpg">
    <meta property="og:site_name" content="RayDesign Technologies">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="RayDesign Technologies | Digital Innovation Partner">
    <meta name="twitter:description" content="Your strategic partner for web development, mobile apps, digital marketing, and AI solutions.">
    <meta name="twitter:image" content="https://raydesigntechnologies.com/images/twitter-card.jpg">

    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
    <meta name="msapplication-TileColor" content="#6b46c1">
    <meta name="theme-color" content="#6b46c1">

    <!-- Canonical URL -->
    <meta rel="canonical" href="https://raydesigntechnologies.com">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- AOS - Animate On Scroll -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                            950: '#2e1065',
                        },
                        'secondary': {
                            50: '#fdf2f8',
                            100: '#fce7f3',
                            200: '#fbcfe8',
                            300: '#f9a8d4',
                            400: '#f472b6',
                            500: '#ec4899',
                            600: '#db2777',
                            700: '#be185d',
                            800: '#9d174d',
                            900: '#831843',
                            950: '#500724',
                        },
                        'dark': '#121212',
                        'light': '#f8f9fa',
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'poppins': ['Poppins', 'sans-serif'],
                        'montserrat': ['Montserrat', 'sans-serif'],
                    },
                    boxShadow: {
                        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
                        'neon': '0 0 5px theme("colors.primary.400"), 0 0 20px theme("colors.primary.700")',
                        'soft': '0 10px 50px -12px rgba(0, 0, 0, 0.25)',
                    },
                    backgroundImage: {
                        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
                        'gradient-conic': 'conic-gradient(var(--tw-gradient-stops))',
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 3s infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-20px)' },
                        }
                    },
                    borderRadius: {
                        'xl': '1rem',
                        '2xl': '1.5rem',
                        '3xl': '2rem',
                        '4xl': '3rem',
                    },
                }
            },
            variants: {
                extend: {
                    opacity: ['group-hover'],
                    transform: ['group-hover'],
                    scale: ['group-hover'],
                    translate: ['group-hover'],
                },
            }
        }
    </script>

    <style>
        /* Custom Styles */
        .bg-glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .bg-glass-dark {
            background: rgba(17, 17, 17, 0.75);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .text-gradient {
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
            background-image: linear-gradient(to right, #7c3aed, #db2777);
        }

        .bg-gradient-purple {
            background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
        }

        .bg-gradient-purple-pink {
            background: linear-gradient(135deg, #5b21b6 0%, #be185d 100%);
        }

        .bg-dots {
            background-image: radial-gradient(#6d28d9 1px, transparent 1px);
            background-size: 20px 20px;
        }

        /* Custom Animations */
        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 15px rgba(139, 92, 246, 0.7);
            }
            50% {
                box-shadow: 0 0 30px rgba(139, 92, 246, 0.9);
            }
        }

        .animate-glow {
            animation: glow 3s ease-in-out infinite;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 10px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #6d28d9;
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #5b21b6;
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Curved Sections */
        .curve-top {
            position: relative;
        }

        .curve-top::before {
            content: '';
            position: absolute;
            top: -50px;
            left: 0;
            width: 100%;
            height: 50px;
            background: inherit;
            border-top-left-radius: 50% 100%;
            border-top-right-radius: 50% 100%;
        }

        .curve-bottom {
            position: relative;
        }

        .curve-bottom::after {
            content: '';
            position: absolute;
            bottom: -50px;
            left: 0;
            width: 100%;
            height: 50px;
            background: inherit;
            border-bottom-left-radius: 50% 100%;
            border-bottom-right-radius: 50% 100%;
        }

        /* Custom Utility Classes */
        .backdrop-blur {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
    </style>
</head>
<body class="font-inter antialiased text-gray-800 overflow-x-hidden bg-white">
    <!-- Preloader -->
    <div id="preloader" class="fixed inset-0 z-50 flex items-center justify-center bg-white">
        <div class="relative w-24 h-24">
            <div class="absolute top-0 left-0 w-full h-full rounded-full border-4 border-t-primary-600 border-r-primary-400 border-b-primary-200 border-l-transparent animate-spin"></div>
            <img src="https://i.postimg.cc/cCwXxx3N/Ray-Design-Logo.png" alt="RayDesign Technologies" class="w-16 h-16 object-contain absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        </div>
    </div>

    <!-- Notification Bar -->
    <div class="bg-gradient-purple text-white py-2 px-4 text-center relative overflow-hidden">
        <div class="animate-marquee whitespace-nowrap flex items-center justify-center">
            <span class="text-sm font-medium mx-4">🎉 Limited Time Offer: Get a FREE 45-minute consultation with our experts! <a href="#contact" class="underline font-bold ml-2 hover:text-white">Book Now</a></span>
            <span class="mx-4">|</span>
            <span class="text-sm font-medium mx-4">🚀 New AI-powered solutions that boost conversion rates by 300% <a href="#services" class="underline font-bold ml-2 hover:text-white">Learn More</a></span>
            <span class="mx-4">|</span>
            <span class="text-sm font-medium mx-4">💼 Trusted by 500+ businesses worldwide <a href="#testimonials" class="underline font-bold ml-2 hover:text-white">See Testimonials</a></span>
        </div>
    </div>

    <!-- Header Navigation -->
    <header id="header" class="sticky top-0 z-40 w-full transition-all duration-300">
        <nav class="bg-glass border-b border-gray-200 backdrop-blur-md">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-20">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <a href="/" class="flex items-center">
                            <img class="h-12 w-auto" src="https://i.postimg.cc/cCwXxx3N/Ray-Design-Logo.png" alt="RayDesign Technologies Logo">
                        </a>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:ml-6 md:flex md:items-center md:space-x-4">
                        <a href="/" class="px-3 py-2 rounded-md text-sm font-medium text-primary-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group">
                            Home
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <a href="/about/" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group">
                            About Us
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"></span>
                        </a>

                        <!-- Services Dropdown -->
                        <div class="relative group">
    <button class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 inline-flex items-center group-hover:text-primary-700">
        Services
        <svg class="ml-1 w-4 h-4 transition-transform duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"></span>
    </button>

    <!-- Dropdown Menu -->
    <div class="absolute left-0 mt-2 w-72 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform origin-top-left group-hover:translate-y-0 translate-y-2 z-50 border border-gray-100">
        <div class="p-4 grid grid-cols-1 gap-2">
            <div class="col-span-1">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">Web & Mobile</h3>
                <a href="/services/web-development/" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150">
                    <i class="fas fa-laptop-code mr-3 text-primary-500"></i>
                    Web Development
                </a>
                <a href="/services/mobile-apps/" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150">
                    <i class="fas fa-mobile-alt mr-3 text-primary-500"></i>
                    Mobile Applications
                </a>
                <a href="/services/e-commerce" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150">
                    <i class="fas fa-shopping-cart mr-3 text-primary-500"></i>
                    E-Commerce Solutions
                </a>
            </div>
        </div>

        <div class="p-4 border-t border-gray-100 grid grid-cols-1 gap-2">
            <div class="col-span-1">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">Marketing</h3>
                <a href="/services/social-media/" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150">
                    <i class="fas fa-share-alt mr-3 text-primary-500"></i>
                    Social Media Marketing
                </a>
                <a href="/services/seo/" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150">
                    <i class="fas fa-search mr-3 text-primary-500"></i>
                    SEO Services
                </a>
                <a href="/services/ppc/" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150">
                    <i class="fas fa-bullseye mr-3 text-primary-500"></i>
                    PPC Advertising
                </a>
            </div>
        </div>

        <div class="p-4 border-t border-gray-100 grid grid-cols-1 gap-2">
            <div class="col-span-1">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">Design & Innovation</h3>
                <a href="/services/ui-ux-design/" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150">
                    <i class="fas fa-pencil-ruler mr-3 text-primary-500"></i>
                    UI/UX Design
                </a>
                <a href="/services/branding/" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150">
                    <i class="fas fa-paint-brush mr-3 text-primary-500"></i>
                    Branding & Identity
                </a>
            </div>
        </div>

        <div class="p-4 border-t border-gray-100 grid grid-cols-1 gap-2">
            <div class="col-span-1">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">AI & Automation</h3>
                <a href="/services/ai-solutions/" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150">
                    <i class="fas fa-robot mr-3 text-primary-500"></i>
                    AI Solutions
                </a>
            </div>
        </div>

        <div class="p-4 bg-primary-50 rounded-b-lg">
            <a href="services.html" class="inline-flex items-center text-sm font-medium text-primary-700 hover:text-primary-900">
                View all services
                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
            </a>
        </div>
    </div>
</div>


                        <a href="/portfolio/" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group">
                            Portfolio
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"></span>
                        </a>

                        <a href="/contact/" class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group">
                            Contact
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>

                    <!-- Action Buttons -->
                    <div class="hidden md:flex items-center">
                        <a href="#contact" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 shadow-md transition-all duration-300 ml-3">
                            Get a Quote
                        </a>
                        <a href="tel:+447878361409" class="inline-flex items-center justify-center px-4 py-2 border border-primary-600 text-sm font-medium rounded-md text-primary-600 bg-transparent hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 ml-3">
                            <i class="fas fa-phone-alt mr-2"></i> Call Us
                        </a>
                    </div>

                    <!-- Mobile menu button -->
                    <div class="flex items-center md:hidden">
                        <button id="mobile-menu-button" type="button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500" aria-expanded="false">
                            <span class="sr-only">Open main menu</span>
                            <svg id="menu-icon" class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                            <svg id="close-icon" class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile menu, show/hide based on menu state -->
            <div id="mobile-menu" class="hidden md:hidden">
                <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white">
                    <a href="/" class="block px-3 py-2 rounded-md text-base font-medium text-primary-700 bg-primary-50">Home</a>
                    <a href="/about/" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">About Us</a>

                   <!-- Mobile Services Accordion -->
<div class="relative">
    <button id="mobile-services-button" class="w-full flex justify-between items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">
        Services
        <svg id="mobile-services-icon" class="h-5 w-5 transform transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
    </button>

    <div id="mobile-services-dropdown" class="hidden px-4 py-2 space-y-1">
        <div class="py-2">
            <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">Web & Mobile</h4>
            <a href="/services/web-development/" class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">Web Development</a>
            <a href="/services/mobile-apps/" class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">Mobile Applications</a>
            <a href="/services/e-commerce" class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">E-Commerce Solutions</a>
        </div>

        <div class="py-2 border-t border-gray-100">
            <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">Marketing</h4>
            <a href="/services/social-media/" class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">Social Media Marketing</a>
            <a href="/services/seo/" class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">SEO Services</a>
            <a href="/services/ppc/" class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">PPC Advertising</a>
        </div>

        <div class="py-2 border-t border-gray-100">
            <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">Design & Innovation</h4>
            <a href="/services/ui-ux-design/" class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">UI/UX Design</a>
            <a href="/services/branding/" class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">Branding & Identity</a>
        </div>

        <div class="py-2 border-t border-gray-100">
            <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">AI & Automation</h4>
            <a href="/services/ai-solutions/" class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">AI Solutions</a>
        </div>

        <div class="py-2 border-t border-gray-100">
            <a href="services.html" class="flex items-center px-3 py-2 text-sm font-medium text-primary-700 hover:text-primary-900">
                View all services
                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
            </a>
        </div>
    </div>
</div>


                    <a href="/portfolio/" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">Portfolio</a>

                    <a href="/contact/" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">Contact</a>
                </div>

                <div class="pt-4 pb-3 border-t border-gray-200">
                    <div class="flex items-center px-4">
                        <div class="flex-shrink-0">
                            <i class="fas fa-headset text-2xl text-primary-600"></i>
                        </div>
                        <div class="ml-3">
                            <div class="text-base font-medium text-gray-800">Need help?</div>
                            <div class="text-sm font-medium text-gray-500">Our experts are available</div>
                        </div>
                    </div>
                    <div class="mt-3 space-y-1 px-2">
                        <a href="tel:+447878361409" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">
                            <i class="fas fa-phone-alt mr-2"></i> +44 7878 361409
                        </a>
                        <a href="mailto:<EMAIL>" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700">
                            <i class="fas fa-envelope mr-2"></i> <EMAIL>
                        </a>
                        <a href="#contact" class="block px-3 py-2 rounded-md text-base font-medium bg-primary-600 text-white hover:bg-primary-700 mt-3 text-center">
                            Get a Quote
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- JavaScript for Header Interactions -->
    <script>
        // Handle header background change on scroll
        const header = document.getElementById('header');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 10) {
                header.classList.add('bg-white', 'shadow-md');
                header.classList.remove('bg-transparent');
            } else {
                header.classList.remove('bg-white', 'shadow-md');
                header.classList.add('bg-transparent');
            }
        });

        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const menuIcon = document.getElementById('menu-icon');
        const closeIcon = document.getElementById('close-icon');

        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
            menuIcon.classList.toggle('hidden');
            closeIcon.classList.toggle('hidden');
        });

        // Mobile services dropdown toggle
        const mobileServicesButton = document.getElementById('mobile-services-button');
        const mobileServicesDropdown = document.getElementById('mobile-services-dropdown');
        const mobileServicesIcon = document.getElementById('mobile-services-icon');

        mobileServicesButton.addEventListener('click', () => {
            mobileServicesDropdown.classList.toggle('hidden');
            mobileServicesIcon.classList.toggle('rotate-180');
        });

        // Preloader
        window.addEventListener('load', () => {
            const preloader = document.getElementById('preloader');
            setTimeout(() => {
                preloader.classList.add('opacity-0');
                setTimeout(() => {
                    preloader.style.display = 'none';
                }, 500);
            }, 1000);
        });
    </script>
    <!-- Main Content for AI & Machine Learning Solutions Service Page -->



<!-- Hero Section 1: Immersive AI Introduction -->
<section class="relative py-20 md:py-28 bg-white overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0 z-0">
        <!-- Neural Network Animation SVG -->
        <svg class="absolute w-full h-full opacity-5" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
            <g class="neural-network">
                <!-- Nodes -->
                <circle cx="150" cy="100" r="5" fill="#6d28d9" class="animate-pulse-slow"/>
                <circle cx="150" cy="200" r="5" fill="#6d28d9" class="animate-pulse-slow"/>
                <circle cx="150" cy="300" r="5" fill="#6d28d9" class="animate-pulse-slow"/>
                <circle cx="150" cy="400" r="5" fill="#6d28d9" class="animate-pulse-slow"/>
                <circle cx="150" cy="500" r="5" fill="#6d28d9" class="animate-pulse-slow"/>

                <circle cx="400" cy="150" r="5" fill="#6d28d9" class="animate-pulse-slow"/>
                <circle cx="400" cy="250" r="5" fill="#6d28d9" class="animate-pulse-slow"/>
                <circle cx="400" cy="350" r="5" fill="#6d28d9" class="animate-pulse-slow"/>
                <circle cx="400" cy="450" r="5" fill="#6d28d9" class="animate-pulse-slow"/>

                <circle cx="650" cy="200" r="5" fill="#6d28d9" class="animate-pulse-slow"/>
                <circle cx="650" cy="300" r="5" fill="#6d28d9" class="animate-pulse-slow"/>
                <circle cx="650" cy="400" r="5" fill="#6d28d9" class="animate-pulse-slow"/>

                <!-- Connections (Layer 1 to Layer 2) -->
                <line x1="150" y1="100" x2="400" y2="150" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="100" x2="400" y2="250" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="100" x2="400" y2="350" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="100" x2="400" y2="450" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>

                <line x1="150" y1="200" x2="400" y2="150" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="200" x2="400" y2="250" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="200" x2="400" y2="350" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="200" x2="400" y2="450" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>

                <line x1="150" y1="300" x2="400" y2="150" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="300" x2="400" y2="250" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="300" x2="400" y2="350" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="300" x2="400" y2="450" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>

                <line x1="150" y1="400" x2="400" y2="150" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="400" x2="400" y2="250" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="400" x2="400" y2="350" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="400" x2="400" y2="450" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>

                <line x1="150" y1="500" x2="400" y2="150" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="500" x2="400" y2="250" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="500" x2="400" y2="350" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="150" y1="500" x2="400" y2="450" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>

                <!-- Connections (Layer 2 to Layer 3) -->
                <line x1="400" y1="150" x2="650" y2="200" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="400" y1="150" x2="650" y2="300" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="400" y1="150" x2="650" y2="400" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>

                <line x1="400" y1="250" x2="650" y2="200" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="400" y1="250" x2="650" y2="300" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="400" y1="250" x2="650" y2="400" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>

                <line x1="400" y1="350" x2="650" y2="200" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="400" y1="350" x2="650" y2="300" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="400" y1="350" x2="650" y2="400" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>

                <line x1="400" y1="450" x2="650" y2="200" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="400" y1="450" x2="650" y2="300" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
                <line x1="400" y1="450" x2="650" y2="400" stroke="#8b5cf6" stroke-width="1" stroke-opacity="0.3"/>
            </g>
        </svg>

        <!-- Abstract AI-themed shapes -->
        <div class="absolute top-20 right-10 w-72 h-72 bg-gradient-to-r from-primary-300/30 to-primary-600/30 rounded-full filter blur-3xl"></div>
        <div class="absolute bottom-20 left-10 w-80 h-80 bg-gradient-to-r from-secondary-300/20 to-secondary-600/20 rounded-full filter blur-3xl"></div>
    </div>



    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Left Column: Hero Text Content -->
            <div class="lg:pr-10" data-aos="fade-right" data-aos-duration="1000">
                <div class="mb-6">
                    <span class="inline-flex items-center px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100">
                        <svg class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 2V4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 20V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M4.93 4.93L6.34 6.34" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M17.66 17.66L19.07 19.07" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2 12H4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M20 12H22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M6.34 17.66L4.93 19.07" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M19.07 4.93L17.66 6.34" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Intelligent Solutions
                    </span>
                </div>

                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-gray-900 mb-6">
                    <span class="text-gradient">AI-Powered</span> Innovation for Business <span class="relative">
                        Growth
                        <svg class="absolute bottom-0 left-0 w-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 150" preserveAspectRatio="none">
                            <path d="M7.7,145.6C109,125,299.9,116.2,401,121.3c42.1,2.2,87.6,11.8,87.3,25.7" stroke="#8b5cf6" stroke-width="4" fill="none" stroke-linecap="round"/>
                        </svg>
                    </span>
                </h1>

                <p class="text-xl text-gray-600 mb-8">
                    Transform your business with custom AI solutions that automate processes, uncover valuable insights, and create intelligent experiences that keep you ahead of the competition.
                </p>

                <div class="flex flex-wrap gap-4 mb-8">
                    <a href="#contact" class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <span>Discuss Your AI Project</span>
                        <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>

                    <a href="#ai-demo" class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-primary-700 bg-white border border-primary-200 hover:bg-primary-50 shadow-md hover:shadow-lg transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="mr-2 w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.7519 11.1679L11.5547 9.03647C10.8901 8.59343 10 9.06982 10 9.86852V14.1315C10 14.9302 10.8901 15.4066 11.5547 14.9635L14.7519 12.8321C15.3457 12.4362 15.3457 11.5638 14.7519 11.1679Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        <span>See AI in Action</span>
                    </a>
                </div>

                <!-- Tech Badges -->
                <div class="flex flex-wrap gap-3 mb-6">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.25 7C9.25 8.24264 8.24264 9.25 7 9.25C5.75736 9.25 4.75 8.24264 4.75 7C4.75 5.75736 5.75736 4.75 7 4.75C8.24264 4.75 9.25 5.75736 9.25 7Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M6.75 9.5V14.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M19.25 17C19.25 18.2426 18.2426 19.25 17 19.25C15.7574 19.25 14.75 18.2426 14.75 17C14.75 15.7574 15.7574 14.75 17 14.75C18.2426 14.75 19.25 15.7574 19.25 17Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M17.25 14.5V9.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M14.75 12C14.75 13.2426 13.7426 14.25 12.5 14.25C11.2574 14.25 10.25 13.2426 10.25 12C10.25 10.7574 11.2574 9.75 12.5 9.75C13.7426 9.75 14.75 10.7574 14.75 12Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M10 12H6.75" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M17.25 12H15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        TensorFlow
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16 12C16 14.2091 14.2091 16 12 16C9.79086 16 8 14.2091 8 12C8 9.79086 9.79086 8 12 8C14.2091 8 16 9.79086 16 12Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M3 8L6 10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M3 16L6 13.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M21 8L18 10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M21 16L18 13.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 2V5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 19V22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        PyTorch
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                        <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 16L12 21L4 16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M20 12L12 17L4 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M20 8L12 13L4 8L12 3L20 8Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Keras
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 21H7.8C6.11984 21 5.27976 21 4.63803 20.673C4.07354 20.3854 3.6146 19.9265 3.32698 19.362C3 18.7202 3 17.8802 3 16.2V3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M17 10L21 14L17 18" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 14H21" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Scikit-Learn
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.5 12.5L10.5 14.5L15.5 9.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M7 3.33782C8.47087 2.48697 10.1786 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 10.1786 2.48697 8.47087 3.33782 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                        BERT
                    </span>
                </div>

                <!-- Trust Indicators -->
                <div data-aos="fade-up" data-aos-delay="300">
                    <div class="flex items-center mb-2">
                        <svg class="w-5 h-5 text-primary-600 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <p class="text-sm text-gray-600">Trusted by leading companies in finance, healthcare, and e-commerce</p>
                    </div>

                    <div class="flex items-center">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <span class="ml-2 text-sm text-gray-600">
                                <span class="font-bold text-gray-900">4.9</span> from 180+ reviews
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column: Interactive AI Illustration -->
            <div class="lg:pl-10" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                <div class="relative">
                    <!-- Main floating brain visualization -->
                    <div class="relative bg-white p-2 rounded-2xl shadow-soft animate-float">
                        <div class="bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl overflow-hidden">
                            <svg class="w-full h-auto" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
                                <!-- Brain outline -->
                                <path d="M250,70 C380,70 380,330 250,330 C120,330 120,70 250,70 Z" fill="none" stroke="#6d28d9" stroke-width="2" opacity="0.7"/>

                                <!-- Left hemisphere connections -->
                                <path d="M190,120 C160,140 160,260 190,280" fill="none" stroke="#6d28d9" stroke-width="2" opacity="0.5"/>
                                <path d="M170,150 C140,170 140,230 170,250" fill="none" stroke="#6d28d9" stroke-width="2" opacity="0.5"/>

                                <!-- Right hemisphere connections -->
                                <path d="M310,120 C340,140 340,260 310,280" fill="none" stroke="#6d28d9" stroke-width="2" opacity="0.5"/>
                                <path d="M330,150 C360,170 360,230 330,250" fill="none" stroke="#6d28d9" stroke-width="2" opacity="0.5"/>

                                <!-- Brain center -->
                                <path d="M250,120 C280,150 280,250 250,280 C220,250 220,150 250,120 Z" fill="none" stroke="#6d28d9" stroke-width="2" opacity="0.6"/>

                                <!-- Neural nodes (pulsing) -->
                                <circle cx="190" cy="120" r="6" fill="#8b5cf6" class="animate-pulse-slow"/>
                                <circle cx="170" cy="150" r="5" fill="#8b5cf6" class="animate-pulse-slow"/>
                                <circle cx="160" cy="200" r="7" fill="#8b5cf6" class="animate-pulse-slow"/>
                                <circle cx="170" cy="250" r="5" fill="#8b5cf6" class="animate-pulse-slow"/>
                                <circle cx="190" cy="280" r="6" fill="#8b5cf6" class="animate-pulse-slow"/>

                                <circle cx="310" cy="120" r="6" fill="#8b5cf6" class="animate-pulse-slow"/>
                                <circle cx="330" cy="150" r="5" fill="#8b5cf6" class="animate-pulse-slow"/>
                                <circle cx="340" cy="200" r="7" fill="#8b5cf6" class="animate-pulse-slow"/>
                                <circle cx="330" cy="250" r="5" fill="#8b5cf6" class="animate-pulse-slow"/>
                                <circle cx="310" cy="280" r="6" fill="#8b5cf6" class="animate-pulse-slow"/>

                                <circle cx="250" cy="120" r="8" fill="#8b5cf6" class="animate-pulse-slow"/>
                                <circle cx="250" cy="200" r="10" fill="#6d28d9" class="animate-pulse-slow"/>
                                <circle cx="250" cy="280" r="8" fill="#8b5cf6" class="animate-pulse-slow"/>

                                <!-- Data flow animations -->
                                <circle cx="190" cy="120" r="3" fill="#fff">
                                    <animate attributeName="cx" from="190" to="250" dur="3s" begin="0s" repeatCount="indefinite"/>
                                    <animate attributeName="cy" from="120" to="200" dur="3s" begin="0s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" from="1" to="0" dur="3s" begin="0s" repeatCount="indefinite"/>
                                </circle>

                                <circle cx="310" cy="120" r="3" fill="#fff">
                                    <animate attributeName="cx" from="310" to="250" dur="4s" begin="1s" repeatCount="indefinite"/>
                                    <animate attributeName="cy" from="120" to="200" dur="4s" begin="1s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" from="1" to="0" dur="4s" begin="1s" repeatCount="indefinite"/>
                                </circle>

                                <circle cx="170" cy="250" r="3" fill="#fff">
                                    <animate attributeName="cx" from="170" to="250" dur="3.5s" begin="0.5s" repeatCount="indefinite"/>
                                    <animate attributeName="cy" from="250" to="200" dur="3.5s" begin="0.5s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" from="1" to="0" dur="3.5s" begin="0.5s" repeatCount="indefinite"/>
                                </circle>

                                <circle cx="330" cy="250" r="3" fill="#fff">
                                    <animate attributeName="cx" from="330" to="250" dur="3.2s" begin="1.5s" repeatCount="indefinite"/>
                                    <animate attributeName="cy" from="250" to="200" dur="3.2s" begin="1.5s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" from="1" to="0" dur="3.2s" begin="1.5s" repeatCount="indefinite"/>
                                </circle>

                                <!-- Binary data flowing -->
                                <text x="160" y="180" font-family="monospace" font-size="8" fill="#6d28d9" opacity="0.7" class="animate-pulse-slow">10110</text>
                                <text x="320" y="180" font-family="monospace" font-size="8" fill="#6d28d9" opacity="0.7" class="animate-pulse-slow">01001</text>
                                <text x="230" y="160" font-family="monospace" font-size="8" fill="#6d28d9" opacity="0.7" class="animate-pulse-slow">11001</text>
                                <text x="240" y="240" font-family="monospace" font-size="8" fill="#6d28d9" opacity="0.7" class="animate-pulse-slow">10101</text>
                            </svg>
                        </div>
                    </div>

                    <!-- Floating elements -->
                    <div class="absolute top-10 -right-5 bg-white rounded-xl shadow-soft p-3 transform rotate-6 animate-float z-10">
                        <div class="flex items-center space-x-2 bg-primary-50 rounded-lg p-2">
                            <svg class="w-8 h-8 text-primary-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.5 11L10.5 13L15.5 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 3H17.5C18.8807 3 20 4.11929 20 5.5V18.5C20 19.8807 18.8807 21 17.5 21H12M12 3H6.5C5.11929 3 4 4.11929 4 5.5V18.5C4 19.8807 5.11929 21 6.5 21H12M12 3V21" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                            <div>
                                <div class="text-xs font-semibold text-primary-700">AI Decision Making</div>
                                <div class="text-xs text-primary-600">98% accuracy</div>
                            </div>
                        </div>
                    </div>

                    <div class="absolute -bottom-5 -left-5 bg-white rounded-xl shadow-soft p-3 transform -rotate-3 animate-float z-10">
                        <div class="flex items-center space-x-2 bg-primary-50 rounded-lg p-2">
                            <svg class="w-8 h-8 text-primary-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 17L15 17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                                <path d="M12 6V13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                                <path d="M19.5 12C19.5 16.1421 16.1421 19.5 12 19.5C7.85786 19.5 4.5 16.1421 4.5 12C4.5 7.85786 7.85786 4.5 12 4.5C16.1421 4.5 19.5 7.85786 19.5 12Z" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                            <div>
                                <div class="text-xs font-semibold text-primary-700">Real-time Analytics</div>
                                <div class="text-xs text-primary-600">Millisecond insights</div>
                            </div>
                        </div>
                    </div>

                    <!-- Data visualization element -->
                    <div class="absolute bottom-12 right-0 bg-white rounded-xl shadow-soft p-3 animate-float">
                        <div class="h-20 w-36 bg-primary-50 rounded-lg p-2 flex items-end justify-between">
                            <div class="w-3 bg-primary-300 rounded-t-sm" style="height: 30%"></div>
                            <div class="w-3 bg-primary-400 rounded-t-sm" style="height: 60%"></div>
                            <div class="w-3 bg-primary-500 rounded-t-sm" style="height: 45%"></div>
                            <div class="w-3 bg-primary-600 rounded-t-sm" style="height: 80%"></div>
                            <div class="w-3 bg-primary-700 rounded-t-sm" style="height: 65%"></div>
                            <div class="w-3 bg-primary-800 rounded-t-sm" style="height: 75%"></div>
                            <div class="w-3 bg-primary-900 rounded-t-sm" style="height: 40%"></div>
                        </div>
                        <div class="mt-1 text-xs text-center text-gray-600">Predictive Modeling</div>
                    </div>

                    <!-- Decorative code snippet -->
                    <div class="absolute top-1/2 -translate-y-1/2 -left-10 bg-white rounded-xl shadow-soft p-2 transform -rotate-6 animate-float-slow z-0">
                        <div class="bg-gray-900 rounded-lg p-2 w-32">
                            <pre class="text-[8px] text-green-400 font-mono">
import tensorflow as tf
model = tf.keras.Sequential([
  tf.keras.layers.Dense(128),
  tf.keras.layers.Dropout(0.2),
  tf.keras.layers.Dense(10)
])
                            </pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- Stats Section: AI Impact -->
<section class="py-16 bg-gradient-to-br from-primary-900 to-primary-700 text-white relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0 z-0 overflow-hidden">
        <!-- Circuit board pattern -->
        <svg class="absolute w-full h-full opacity-10" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <pattern id="circuit-pattern" patternUnits="userSpaceOnUse" width="20" height="20">
                <path d="M0,10 L20,10 M10,0 L10,20 M3,3 L7,7 M13,3 L17,7 M3,17 L7,13 M13,17 L17,13" stroke="white" stroke-width="0.5" fill="none" />
                <circle cx="10" cy="10" r="2" fill="white" opacity="0.5" />
                <circle cx="0" cy="0" r="1" fill="white" opacity="0.5" />
                <circle cx="0" cy="20" r="1" fill="white" opacity="0.5" />
                <circle cx="20" cy="0" r="1" fill="white" opacity="0.5" />
                <circle cx="20" cy="20" r="1" fill="white" opacity="0.5" />
            </pattern>
            <rect width="100%" height="100%" fill="url(#circuit-pattern)" />
        </svg>

        <!-- Glowing orbs -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-white rounded-full filter blur-3xl opacity-10"></div>
        <div class="absolute bottom-0 left-0 w-96 h-96 bg-white rounded-full filter blur-3xl opacity-10"></div>
    </div>



    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-12" data-aos="fade-up">
            <h2 class="text-3xl sm:text-4xl font-bold mb-4">The Business Impact of AI</h2>
            <p class="text-xl text-primary-100 max-w-3xl mx-auto">
                Our AI solutions deliver measurable results across operations, customer experience, and revenue growth.
            </p>
        </div>



        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Stat 1: Automation -->
            <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center transform transition-all duration-300 hover:scale-105 hover:bg-white/20" data-aos="fade-up" data-aos-delay="100">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
                    <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 15C14 16.1046 13.1046 17 12 17C10.8954 17 10 16.1046 10 15C10 13.8954 10.8954 13 12 13C13.1046 13 14 13.8954 14 15Z" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M14 7C14 8.10457 13.1046 9 12 9C10.8954 9 10 8.10457 10 7C10 5.89543 10.8954 5 12 5C13.1046 5 14 5.89543 14 7Z" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M22 15C22 16.1046 21.1046 17 20 17C18.8954 17 18 16.1046 18 15C18 13.8954 18.8954 13 20 13C21.1046 13 22 13.8954 22 15Z" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M6 15C6 16.1046 5.10457 17 4 17C2.89543 17 2 16.1046 2 15C2 13.8954 2.89543 13 4 13C5.10457 13 6 13.8954 6 15Z" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M14 7H18C19.1046 7 20 7.89543 20 9V13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M10 7H6C4.89543 7 4 7.89543 4 9V13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="text-5xl font-bold mb-2">68%</div>
                <div class="text-xl font-semibold mb-3">Task Automation</div>
                <p class="text-primary-100">of repetitive business processes automated with custom AI solutions</p>
            </div>



            <!-- Stat 2: Response Time -->
            <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center transform transition-all duration-300 hover:scale-105 hover:bg-white/20" data-aos="fade-up" data-aos-delay="200">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
                    <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 8V12L14.5 14.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M5.60414 5.60414L5.6172 5.61721" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M18.3954 5.60414L18.3823 5.61721" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M5.60414 18.3954L5.6172 18.3823" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M18.3954 18.3954L18.3823 18.3823" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="text-5xl font-bold mb-2">2.8s</div>
                <div class="text-xl font-semibold mb-3">Response Time</div>
                <p class="text-primary-100">average chatbot response time, with 24/7 availability for customer inquiries</p>
            </div>



            <!-- Stat 3: Accuracy -->
            <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center transform transition-all duration-300 hover:scale-105 hover:bg-white/20" data-aos="fade-up" data-aos-delay="300">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
                    <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.5 11L10.5 13L15.5 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M7 19L5 19C3.89543 19 3 18.1046 3 17L3 7C3 5.89543 3.89543 5 5 5L19 5C20.1046 5 21 5.89543 21 7L21 17C21 18.1046 20.1046 19 19 19L17 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </div>
                <div class="text-5xl font-bold mb-2">94%</div>
                <div class="text-xl font-semibold mb-3">Prediction Accuracy</div>
                <p class="text-primary-100">for our predictive analytics models, driving better business decisions</p>
            </div>



            <!-- Stat 4: ROI -->
            <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center transform transition-all duration-300 hover:scale-105 hover:bg-white/20" data-aos="fade-up" data-aos-delay="400">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
                    <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 18C8.68629 18 6 15.3137 6 12C6 8.68629 8.68629 6 12 6C15.3137 6 18 8.68629 18 12C18 15.3137 15.3137 18 12 18Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M10 12H12V15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 9H12.01" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M3 9H5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M3 15H5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M21 9H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M21 15H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M9 3L7.5 4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M9 21L7.5 19.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M15 3L16.5 4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M15 21L16.5 19.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="text-5xl font-bold mb-2">327%</div>
                <div class="text-xl font-semibold mb-3">ROI</div>
                <p class="text-primary-100">average return on investment within the first year of AI implementation</p>
            </div>
        </div>



        <div class="mt-12 text-center" data-aos="fade-up" data-aos-delay="500">
            <a href="#case-studies" class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-primary-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                <span>Explore Case Studies</span>
                <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </div>
    </div>
</section>



<!-- Hero Section 2: AI Services Showcase -->
<section class="py-20 bg-gray-50 relative overflow-hidden">
    <!-- Background Decorative Elements -->
    <div class="absolute inset-0 z-0">
        <!-- Abstract data pattern -->
        <div class="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-b from-primary-50 to-transparent opacity-70"></div>
        <div class="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-t from-primary-50 to-transparent opacity-70"></div>

        <!-- Binary code pattern -->
        <div class="absolute top-20 left-10 text-primary-200 opacity-20 text-xs font-mono leading-tight">
            01001010 10101010<br>
            10101010 10101010<br>
            01010101 01010101<br>
            10101010 10101010<br>
            01010101 01010101
        </div>

        <div class="absolute bottom-20 right-10 text-primary-200 opacity-20 text-xs font-mono leading-tight">
            01001010 10101010<br>
            10101010 10101010<br>
            01010101 01010101<br>
            10101010 10101010<br>
            01010101 01010101
        </div>
    </div>



    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16" data-aos="fade-up">
            <span class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4">
                Intelligent Solutions
            </span>
            <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Transformative <span class="text-gradient">AI Technologies</span> for Your Business
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Our custom AI solutions are designed to solve complex business challenges, automate processes, and uncover actionable insights from your data.
            </p>
        </div>



        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <!-- Chatbots Card -->
            <div class="bg-white rounded-2xl shadow-soft p-8 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-2" data-aos="fade-up" data-aos-delay="100">
                <div class="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-primary-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 10.5H8.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        <path d="M16 10.5H16.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        <path d="M8.5 14.5C8.5 14.5 9.5 15.5 12 15.5C14.5 15.5 15.5 14.5 15.5 14.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        <path d="M19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12Z" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M19.25 8.75V4.75H15.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M4.75 15.25V19.25H8.75" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Intelligent Chatbots</h3>
                <p class="text-gray-600 mb-6">
                    Conversational AI that understands context, learns from interactions, and delivers human-like support across multiple channels 24/7.
                </p>
                <div class="space-y-3 mb-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-primary-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Natural language understanding</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-primary-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Continuous learning & improvement</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-primary-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Seamless system integration</span>
                    </div>
                </div>

                <!-- Chat demo preview -->
                <div class="bg-gray-50 rounded-xl p-3 mb-6">
                    <div class="flex items-start space-x-2 mb-2">
                        <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-primary-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="bg-white rounded-lg p-2 text-xs shadow-sm">
                            Hello! How can I help you today with your order?
                        </div>
                    </div>
                    <div class="flex items-start space-x-2 justify-end">
                        <div class="bg-primary-100 rounded-lg p-2 text-xs">
                            I'd like to check my shipping status
                        </div>
                        <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-gray-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z" fill="currentColor"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <a href="#chatbots" class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium">
                    Learn more
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>



            <!-- Predictive Analytics Card -->
            <div class="bg-white rounded-2xl shadow-soft p-8 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-2" data-aos="fade-up" data-aos-delay="200">
                <div class="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-primary-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        <path d="M12 7L12 12L9 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M5.5 3C5.5 3 6.5 7.5 3 7.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Predictive Analytics</h3>
                <p class="text-gray-600 mb-6">
                    Harness the power of your data to forecast trends, anticipate customer needs, and make proactive business decisions.
                </p>
                <div class="space-y-3 mb-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-primary-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Advanced forecasting models</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-primary-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Pattern recognition</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-primary-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Anomaly detection</span>
                    </div>
                </div>

                <!-- Chart preview -->
                <div class="bg-gray-50 rounded-xl p-3 mb-6">
                    <div class="h-28 flex items-end space-x-1">
                        <div class="w-full h-full flex items-end space-x-1">
                            <div class="w-1/12 bg-primary-200 rounded-t" style="height: 30%"></div>
                            <div class="w-1/12 bg-primary-300 rounded-t" style="height: 50%"></div>
                            <div class="w-1/12 bg-primary-400 rounded-t" style="height: 40%"></div>
                            <div class="w-1/12 bg-primary-500 rounded-t" style="height: 60%"></div>
                            <div class="w-1/12 bg-primary-600 rounded-t" style="height: 45%"></div>
                            <div class="w-1/12 bg-primary-700 rounded-t" style="height: 70%"></div>
                            <div class="w-1/12 bg-primary-600 rounded-t" style="height: 65%"></div>
                            <div class="w-1/12 bg-primary-500 rounded-t" style="height: 80%"></div>
                            <div class="w-1/12 bg-primary-400 rounded-t" style="height: 75%"></div>
                            <div class="w-1/12 bg-primary-300 rounded-t" style="height: 90%"></div>
                            <div class="w-1/12 bg-primary-200 rounded-t" style="height: 65%"></div>
                            <div class="w-1/12 bg-primary-200 rounded-t" style="height: 70%"></div>
                        </div>
                    </div>
                    <div class="h-1 w-full bg-gray-200 mt-1"></div>
                    <div class="flex justify-between mt-1 text-xs text-gray-500">
                        <span>Jan</span>
                        <span>Dec</span>
                    </div>
                    <div class="flex justify-between mt-1">
                        <span class="text-xs text-gray-500">Historical Data</span>
                        <span class="text-xs text-primary-600">Prediction</span>
                    </div>
                </div>

                <a href="#predictive-analytics" class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium">
                    Learn more
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>



            <!-- NLP Card -->
            <div class="bg-white rounded-2xl shadow-soft p-8 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-2" data-aos="fade-up" data-aos-delay="300">
                <div class="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-primary-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19.5 5.5V12.5C19.5 16.366 16.366 19.5 12.5 19.5H5.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M4.5 15H5.25C5.66421 15 6 15.3358 6 15.75V16.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M4.5 12H5.25C5.66421 12 6 11.6642 6 11.25V10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M4.5 9H5.25C5.66421 9 6 8.66421 6 8.25V7.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M9 4.5V5.25C9 5.66421 8.66421 6 8.25 6H7.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 4.5V5.25C12 5.66421 12.3358 6 12.75 6H13.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M15 4.5V5.25C15 5.66421 15.3358 6 15.75 6H16.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Natural Language Processing</h3>
                <p class="text-gray-600 mb-6">
                    Extract insights from text data, understand customer sentiment, and automate document processing with our advanced NLP solutions.
                </p>
                <div class="space-y-3 mb-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-primary-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Sentiment analysis</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-primary-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Entity recognition</span>
                    </div>
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-primary-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Multilingual capabilities</span>
                    </div>
                </div>

                <!-- NLP demo preview -->
                <div class="bg-gray-50 rounded-xl p-3 mb-6">
                    <div class="text-xs text-gray-700 mb-2">
                        "Your customer support team was extremely helpful and resolved my issue quickly!"
                    </div>
                    <div class="flex space-x-2 mb-1">
                        <div class="text-xs text-gray-500">Sentiment:</div>
                        <div class="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full">Positive (92%)</div>
                    </div>
                    <div class="flex space-x-2">
                        <div class="text-xs text-gray-500">Entities:</div>
                        <div class="flex space-x-1">
                            <div class="text-xs px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full">customer support</div>
                            <div class="text-xs px-2 py-0.5 bg-purple-100 text-purple-800 rounded-full">issue</div>
                        </div>
                    </div>
                </div>

                <a href="#nlp" class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium">
                    Learn more
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>
        </div>



        <div class="text-center" data-aos="fade-up" data-aos-delay="400">
            <a href="#ai-consultation" class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <span>Schedule an AI Consultation</span>
                <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </div>
    </div>
</section>



<!-- Initialize AOS (Animate On Scroll) -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    });
</script>
<!-- About Us Section with Company Story -->
<section id="about-us" class="py-20 bg-white relative overflow-hidden">
    <!-- Background Decoration Elements -->
    <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blob -->
        <div class="absolute -top-32 -right-32 w-96 h-96 bg-gradient-to-br from-primary-100 to-primary-300 rounded-full filter blur-3xl opacity-30 animate-float"></div>
        <div class="absolute -bottom-32 -left-32 w-96 h-96 bg-gradient-to-tr from-secondary-100 to-secondary-300 rounded-full filter blur-3xl opacity-30 animate-float" style="animation-delay: 2s;"></div>

        <!-- SVG Pattern Background -->
        <div class="absolute inset-0 opacity-5">
            <svg width="100%" height="100%">
                <pattern id="pattern-circles" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse" patternContentUnits="userSpaceOnUse">
                    <circle id="pattern-circle" cx="20" cy="20" r="3.5" fill="#6d28d9"></circle>
                </pattern>
                <rect x="0" y="0" width="100%" height="100%" fill="url(#pattern-circles)"></rect>
            </svg>
        </div>

        <!-- SVG Wave Shape -->
        <svg class="absolute top-0 left-0 w-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
            <path fill="rgba(237, 233, 254, 0.5)" fill-opacity="1" d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,85.3C672,75,768,85,864,112C960,139,1056,181,1152,181.3C1248,181,1344,139,1392,117.3L1440,96L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z"></path>
        </svg>
    </div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Left Column: Company Story -->
            <div class="lg:pr-10" data-aos="fade-right" data-aos-delay="100" data-aos-duration="1000">
                <span class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4">
                    Our Story
                </span>

                <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-6">
                    Not Just a Digital Agency—A <span class="text-gradient">Future-Forging</span> Innovation Powerhouse
                </h2>

                <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                    At RayDesign Technologies, we are a strategic transformation partner that merges the best of design thinking, intelligent automation, full-stack engineering, and marketing science to architect high-impact, human-centered digital ecosystems.
                </p>

                <p class="text-lg text-gray-600 mb-8 leading-relaxed">
                    Our ethos is rooted in transforming complex, chaotic business challenges into seamless, scalable, and emotionally engaging digital experiences—fueling business growth, operational efficiency, and brand transcendence in a fast-paced, hyper-digital world.
                </p>

                <!-- Company Values -->
                <div class="space-y-6 mb-8">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 mt-1">
                            <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M5.05 3.636a1 1 0 010 1.414 7 7 0 000 9.9 1 1 0 11-1.414 1.414 9 9 0 010-12.728 1 1 0 011.414 0zm9.9 0a1 1 0 011.414 0 9 9 0 010 12.728 1 1 0 11-1.414-1.414 7 7 0 000-9.9 1 1 0 010-1.414zM7.879 6.464a1 1 0 010 1.414 3 3 0 000 4.243 1 1 0 11-1.415 1.414 5 5 0 010-7.07 1 1 0 011.415 0zm4.242 0a1 1 0 011.415 0 5 5 0 010 7.072 1 1 0 01-1.415-1.415 3 3 0 000-4.242 1 1 0 010-1.415z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Innovation at Core</h3>
                            <p class="text-gray-600">We constantly push boundaries, embracing emerging technologies and methodologies to deliver cutting-edge solutions.</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="flex-shrink-0 mt-1">
                            <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Human-Centered Approach</h3>
                            <p class="text-gray-600">We design with empathy, putting users at the center of everything we create to build meaningful digital experiences.</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="flex-shrink-0 mt-1">
                            <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Collaborative Excellence</h3>
                            <p class="text-gray-600">We work closely with our clients as true partners, combining our expertise with their industry knowledge.</p>
                        </div>
                    </div>
                </div>

                <!-- CTA Button -->
                <a href="#contact" class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <span>Partner With Us</span>
                    <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>

            <!-- Right Column: Image Collage -->
            <div class="lg:pl-10 relative" data-aos="fade-left" data-aos-delay="300" data-aos-duration="1000">
                <div class="relative h-[500px] w-full">
                    <!-- Main Image -->
                    <div class="absolute top-0 right-0 w-[75%] h-[65%] rounded-2xl overflow-hidden shadow-xl z-20 animate-float" style="animation-delay: 0.5s;">
                        <img src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?auto=format&fit=crop&w=800&q=80" alt="RayDesign Team Collaboration" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-gradient-to-tr from-primary-900/40 to-transparent"></div>
                    </div>

                    <!-- Secondary Image -->
                    <div class="absolute bottom-0 left-0 w-[65%] h-[55%] rounded-2xl overflow-hidden shadow-xl z-10 animate-float" style="animation-delay: 1s;">
                        <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?auto=format&fit=crop&w=800&q=80" alt="RayDesign Office Culture" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-gradient-to-tr from-primary-900/40 to-transparent"></div>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute top-[40%] left-[20%] w-16 h-16 rounded-full bg-primary-100 z-30 animate-pulse flex items-center justify-center">
                        <svg class="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                    </div>

                    <!-- Stats Card -->
                    <div class="absolute -bottom-6 right-10 bg-white rounded-xl shadow-lg p-4 z-30">
                        <div class="grid grid-cols-2 gap-6">
                            <div class="text-center">
                                <h4 class="text-3xl font-bold text-primary-600">8+</h4>
                                <p class="text-gray-600 text-sm">Years Experience</p>
                            </div>
                            <div class="text-center">
                                <h4 class="text-3xl font-bold text-primary-600">500+</h4>
                                <p class="text-gray-600 text-sm">Happy Clients</p>
                            </div>
                        </div>
                    </div>

                    <!-- Pattern Background -->
                    <div class="absolute -bottom-4 -right-4 w-32 h-32 bg-dots opacity-20 z-0"></div>
                </div>
            </div>
        </div>

        <!-- Mission & Vision Section -->
        <div class="mt-24 grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Mission Card -->
            <div class="bg-white rounded-2xl p-8 shadow-soft border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1" data-aos="fade-up" data-aos-delay="100">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900">Our Mission</h3>
                </div>
                <p class="text-gray-600 leading-relaxed mb-6">
                    To empower businesses through innovative digital solutions that solve complex challenges, enhance user experiences, and drive sustainable growth. We are committed to delivering excellence through a perfect blend of creativity, technology, and strategic thinking.
                </p>
                <div class="pt-4 border-t border-gray-100">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?auto=format&fit=crop&w=100&h=100&q=80" alt="CEO" class="w-10 h-10 rounded-full object-cover mr-3">

                    </div>
                </div>
            </div>

            <!-- Vision Card -->
            <div class="bg-white rounded-2xl p-8 shadow-soft border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1" data-aos="fade-up" data-aos-delay="200">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 rounded-full bg-secondary-100 flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-secondary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900">Our Vision</h3>
                </div>
                <p class="text-gray-600 leading-relaxed mb-6">
                    To be the global leader in digital innovation, recognized for transforming businesses through cutting-edge technology solutions. We envision a world where every business, regardless of size, has access to the tools and expertise needed to thrive in the digital landscape.
                </p>
                <div class="pt-4 border-t border-gray-100">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?auto=format&fit=crop&w=100&h=100&q=80" alt="CTO" class="w-10 h-10 rounded-full object-cover mr-3">
                        <div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Process Section -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 pt-24 relative z-10">
        <div class="text-center max-w-3xl mx-auto mb-16" data-aos="fade-up">
            <span class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4">
                Our Process
            </span>
            <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">Our Process of <span class="text-gradient">Excellence</span></h2>
            <p class="text-xl text-gray-600">
                At RayDesign Technologies, we follow a multi-disciplinary and precision-engineered process that ensures every digital solution is not only beautiful and intelligent—but strategically aligned with measurable business outcomes.
            </p>
        </div>

        <!-- Process Timeline -->
        <div class="relative">
            <!-- Vertical Line -->
            <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-primary-400 to-primary-600 rounded-full hidden md:block"></div>

            <div class="space-y-12">
                <!-- Process Step 1 -->
                <div class="relative" data-aos="fade-up" data-aos-delay="100">
                    <div class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"></div>

                    <div class="flex flex-col md:flex-row items-center">
                        <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
                            <div class="bg-white rounded-2xl p-6 shadow-soft inline-block">
                                <h3 class="text-xl font-bold text-gray-900 mb-3 flex items-center justify-end">
                                    <span>Discovery & Strategic Alignment</span>
                                    <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center ml-4 flex-shrink-0">
                                        <span class="text-primary-600 font-bold">1</span>
                                    </div>
                                </h3>
                                <p class="text-gray-600 mb-4">We begin with a comprehensive discovery phase to understand your business objectives, target audience, and competitive landscape.</p>
                                <ul class="space-y-2 text-right">
                                    <li class="flex items-center justify-end">
                                        <span>Deep-dive brand immersion workshops</span>
                                        <svg class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </li>
                                    <li class="flex items-center justify-end">
                                        <span>Industry benchmarking and competitor gap analysis</span>
                                        <svg class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </li>
                                    <li class="flex items-center justify-end">
                                        <span>Stakeholder interviews and KPI alignment</span>
                                        <svg class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="md:w-1/2 md:pl-12">
                            <div class="rounded-2xl overflow-hidden shadow-lg">
                                <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?auto=format&fit=crop&w=800&q=80" alt="Discovery Phase" class="w-full h-64 object-cover">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Process Step 2 -->
                <div class="relative" data-aos="fade-up" data-aos-delay="200">
                    <div class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"></div>

                    <div class="flex flex-col md:flex-row items-center">
                        <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0 md:order-2">
                            <div class="bg-white rounded-2xl p-6 shadow-soft inline-block">
                                <h3 class="text-xl font-bold text-gray-900 mb-3 flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-4 flex-shrink-0">
                                        <span class="text-primary-600 font-bold">2</span>
                                    </div>
                                    <span>Ideation & Prototyping</span>
                                </h3>
                                <p class="text-gray-600 mb-4">We transform insights into actionable concepts, rapidly prototyping solutions to validate ideas before full development.</p>
                                <ul class="space-y-2">
                                    <li class="flex items-center">
                                        <svg class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>Design sprints to rapidly visualize core functionalities</span>
                                    </li>
                                    <li class="flex items-center">
                                        <svg class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>Wireframing, low-fidelity UX mapping, and journey visualization</span>
                                    </li>
                                    <li class="flex items-center">
                                        <svg class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>Stakeholder validation loops for early feedback</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="md:w-1/2 md:pl-12 md:order-1">
                            <div class="rounded-2xl overflow-hidden shadow-lg">
                                <img src="https://images.unsplash.com/photo-1542744173-05336fcc7ad4?auto=format&fit=crop&w=800&q=80" alt="Ideation Phase" class="w-full h-64 object-cover">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Process Step 3 -->
                <div class="relative" data-aos="fade-up" data-aos-delay="300">
                    <div class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"></div>

                    <div class="flex flex-col md:flex-row items-center">
                        <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
                            <div class="bg-white rounded-2xl p-6 shadow-soft inline-block">
                                <h3 class="text-xl font-bold text-gray-900 mb-3 flex items-center justify-end">
                                    <span>Engineering & Implementation</span>
                                    <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center ml-4 flex-shrink-0">
                                        <span class="text-primary-600 font-bold">3</span>
                                    </div>
                                </h3>
                                <p class="text-gray-600 mb-4">Our engineering team brings concepts to life using the latest technologies and best practices for robust, scalable solutions.</p>
                                <ul class="space-y-2 text-right">
                                    <li class="flex items-center justify-end">
                                        <span>Agile sprint cycles with CI/CD pipelines</span>
                                        <svg class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </li>
                                    <li class="flex items-center justify-end">
                                        <span>Secure codebases, microservices architecture, and API-first development</span>
                                        <svg class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </li>
                                    <li class="flex items-center justify-end">
                                        <span>QA automation, manual testing, and continuous optimization</span>
                                        <svg class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="md:w-1/2 md:pl-12">
                            <div class="rounded-2xl overflow-hidden shadow-lg">
                                <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?auto=format&fit=crop&w=800&q=80" alt="Engineering Phase" class="w-full h-64 object-cover">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Process Step 4 -->
                <div class="relative" data-aos="fade-up" data-aos-delay="400">
                    <div class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"></div>

                    <div class="flex flex-col md:flex-row items-center">
                        <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0 md:order-2">
                            <div class="bg-white rounded-2xl p-6 shadow-soft inline-block">
                                <h3 class="text-xl font-bold text-gray-900 mb-3 flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-4 flex-shrink-0">
                                        <span class="text-primary-600 font-bold">4</span>
                                    </div>
                                    <span>Launch & Growth Enablement</span>
                                </h3>
                                <p class="text-gray-600 mb-4">We ensure a smooth launch and provide ongoing support to maximize ROI and drive sustainable growth.</p>
                                <ul class="space-y-2">
                                    <li class="flex items-center">
                                        <svg class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>Performance monitoring dashboards, SEO/ASO readiness</span>
                                    </li>
                                    <li class="flex items-center">
                                        <svg class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>A/B tested go-to-market campaigns</span>
                                    </li>
                                    <li class="flex items-center">
                                        <svg class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>Feedback-driven iterations and lifecycle automation</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="md:w-1/2 md:pl-12 md:order-1">
                            <div class="rounded-2xl overflow-hidden shadow-lg">
                                <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=800&q=80" alt="Launch Phase" class="w-full h-64 object-cover">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section with Animated Counters -->
<section class="py-16 bg-gradient-purple text-white relative overflow-hidden">
    <!-- Background Decoration Elements -->
    <div class="absolute inset-0 z-0">
        <div class="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-white to-transparent opacity-10"></div>
        <div class="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-white to-transparent opacity-10"></div>

        <!-- Abstract Pattern Background -->
        <div class="absolute inset-0 opacity-10">
            <svg width="100%" height="100%">
                <pattern id="pattern-hexagons" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse" patternContentUnits="userSpaceOnUse">
                    <path id="pattern-hexagon" d="M20,0 L40,10 L40,30 L20,40 L0,30 L0,10 Z" fill="none" stroke="white" stroke-width="1"></path>
                </pattern>
                <rect x="0" y="0" width="100%" height="100%" fill="url(#pattern-hexagons)"></rect>
            </svg>
        </div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Stat 1 -->
            <div class="text-center" data-aos="fade-up" data-aos-delay="100">
                <div class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                    </svg>
                </div>
                <h3 class="text-4xl font-bold mb-2"><span class="counter" data-target="500">0</span>+</h3>
                <p class="text-white/80">Happy Clients</p>
            </div>

            <!-- Stat 2 -->
            <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                <div class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                    </svg>
                </div>
                <h3 class="text-4xl font-bold mb-2"><span class="counter" data-target="850">0</span>+</h3>
                <p class="text-white/80">Projects Completed</p>
            </div>

            <!-- Stat 3 -->
            <div class="text-center" data-aos="fade-up" data-aos-delay="300">
                <div class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-4xl font-bold mb-2"><span class="counter" data-target="8">0</span>+</h3>
                <p class="text-white/80">Years Experience</p>
            </div>

            <!-- Stat 4 -->
            <div class="text-center" data-aos="fade-up" data-aos-delay="400">
                <div class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                    </svg>
                </div>
                <h3 class="text-4xl font-bold mb-2"><span class="counter" data-target="35">0</span>+</h3>
                <p class="text-white/80">Expert Team Members</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA and Contact Section -->
<section id="contact" class="py-20 relative overflow-hidden">
    <!-- Background Decoration Elements -->
    <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blobs -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-primary-300/30 to-primary-600/30 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"></div>
        <div class="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-secondary-300/30 to-secondary-600/30 rounded-full filter blur-3xl opacity-30 animate-float" style="animation-delay: 1.5s;"></div>

        <!-- SVG Pattern Background -->
        <div class="absolute inset-0 opacity-5">
            <svg width="100%" height="100%">
                <pattern id="contact-dots" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse" patternContentUnits="userSpaceOnUse">
                    <circle id="contact-dot" cx="15" cy="15" r="1" fill="#6d28d9"></circle>
                </pattern>
                <rect x="0" y="0" width="100%" height="100%" fill="url(#contact-dots)"></rect>
            </svg>
        </div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Call to Action Section -->
        <div class="relative mb-24" data-aos="fade-up">
            <div class="bg-gradient-purple-pink rounded-3xl overflow-hidden shadow-xl">
                <div class="relative p-8 md:p-12 lg:p-16">
                    <!-- Background Decorations -->
                    <div class="absolute top-0 right-0 w-64 h-64 bg-white rounded-full opacity-10 transform translate-x-1/3 -translate-y-1/3"></div>
                    <div class="absolute bottom-0 left-0 w-64 h-64 bg-white rounded-full opacity-10 transform -translate-x-1/3 translate-y-1/3"></div>

                    <!-- Abstract SVG Shape -->
                    <svg class="absolute bottom-0 right-0 w-80 h-80 text-white opacity-10" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <path fill="currentColor" d="M44.3,-76.5C58.4,-71.2,71.3,-61.3,79.4,-48.1C87.5,-34.8,90.8,-18.4,90.3,-2.4C89.8,13.5,85.6,27.1,77.2,38.2C68.9,49.3,56.3,58,43.1,65.5C29.9,73,14.9,79.4,0.3,78.9C-14.3,78.5,-28.7,71.3,-42.7,63.3C-56.7,55.3,-70.4,46.4,-78.8,33.5C-87.2,20.6,-90.3,3.6,-87.4,-12.1C-84.5,-27.8,-75.5,-42.2,-63.9,-53.4C-52.3,-64.5,-38.1,-72.3,-23.6,-76.9C-9.1,-81.5,5.7,-82.9,19.4,-80.8C33.1,-78.7,45.7,-73.2,44.3,-76.5Z" transform="translate(100 100)" />
                    </svg>

                    <div class="grid grid-cols-1 lg:grid-cols-5 gap-10 items-center">
                        <div class="lg:col-span-3">
                            <h2 class="text-3xl md:text-4xl font-extrabold text-white mb-6">Ready to Transform Your Digital Presence?</h2>
                            <p class="text-white/90 text-xl mb-8 max-w-2xl">Partner with RayDesign Technologies to create impactful digital experiences that drive growth, enhance user engagement, and boost your business results.</p>

                            <div class="space-y-4 mb-8">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 mt-1 bg-white/20 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <p class="ml-3 text-white/90">Expert team with proven track record across industries</p>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 mt-1 bg-white/20 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <p class="ml-3 text-white/90">Innovative solutions that keep you ahead of the competition</p>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-6 h-6 mt-1 bg-white/20 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <p class="ml-3 text-white/90">Comprehensive solutions that grow with your business</p>
                                </div>
                            </div>

                            <div class="flex flex-wrap gap-4">
                                <a href="#contactForm" class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-primary-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                                    <span>Get Started Today</span>
                                    <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                                <a href="tel:+447878361409" class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white border border-white/30 backdrop-blur-sm hover:bg-white/10 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                    </svg>
                                    <span>Call Us Now</span>
                                </a>
                            </div>
                        </div>

                        <div class="lg:col-span-2">
                            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                                <h3 class="text-xl font-bold text-white mb-4">Get a Free Consultation</h3>
                                <p class="text-white/80 mb-6">Schedule a 45-minute consultation with our experts to discuss your project needs.</p>

                                <form class="space-y-4">
                                    <div>
                                        <label for="quick-name" class="sr-only">Your Name</label>
                                        <input type="text" id="quick-name" name="quick-name" class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50" placeholder="Your Name">
                                    </div>
                                    <div>
                                        <label for="quick-email" class="sr-only">Your Email</label>
                                        <input type="email" id="quick-email" name="quick-email" class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50" placeholder="Your Email">
                                    </div>
                                    <div>
                                        <label for="quick-service" class="sr-only">Service Interested In</label>
                                        <select id="quick-service" name="quick-service" class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50">
                                            <option value="" disabled selected class="text-gray-500">Service Interested In</option>
                                            <option value="web-development" class="text-gray-800">Web Development</option>
                                            <option value="mobile-app" class="text-gray-800">Mobile App Development</option>
                                            <option value="ui-ux" class="text-gray-800">UI/UX Design</option>
                                            <option value="digital-marketing" class="text-gray-800">Digital Marketing</option>
                                            <option value="ai-solutions" class="text-gray-800">AI Solutions</option>
                                            <option value="other" class="text-gray-800">Other Services</option>
                                        </select>
                                    </div>

                                    <button type="submit" class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium rounded-lg text-primary-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                                        Book My Free Consultation
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wave Separator -->
            <svg class="absolute -bottom-1 left-0 w-full text-white" viewBox="0 0 1440 96" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0,96L60,80C120,64,240,32,360,32C480,32,600,64,720,64C840,64,960,32,1080,24C1200,16,1320,32,1380,40L1440,48L1440,96L1380,96C1320,96,1200,96,1080,96C960,96,840,96,720,96C600,96,480,96,360,96C240,96,120,96,60,96L0,96Z" fill="currentColor"></path>
            </svg>
        </div>

        <!-- Contact Us Section -->
        <div id="contactForm" class="mt-16" data-aos="fade-up" data-aos-delay="100">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <span class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4">
                    Get In Touch
                </span>
                <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">Let's Discuss Your <span class="text-gradient">Project</span></h2>
                <p class="text-xl text-gray-600">Fill out the form below, and our team will get back to you within 24 hours to discuss how we can help bring your vision to life.</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                <!-- Contact Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-2xl shadow-soft p-8 border border-gray-100">
                        <form class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Your Name</label>
                                    <input type="text" id="name" name="name" class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300" placeholder="John Smith">
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                    <input type="email" id="email" name="email" class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300" placeholder="<EMAIL>">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300" placeholder="+44 7700 900000">
                                </div>
                                <div>
                                    <label for="company" class="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                                    <input type="text" id="company" name="company" class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300" placeholder="Your Company Ltd">
                                </div>
                            </div>

                            <div>
                                <label for="service" class="block text-sm font-medium text-gray-700 mb-1">Service You're Interested In</label>
                                <select id="service" name="service" class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300">
                                    <option value="" disabled selected>Select a Service</option>
                                    <option value="web-development">Web Design & Development</option>
                                    <option value="mobile-app">Mobile App Development</option>
                                    <option value="ecommerce">E-Commerce Solutions</option>
                                    <option value="ui-ux">UI/UX Design</option>
                                    <option value="branding">Branding & Identity</option>
                                    <option value="digital-marketing">Digital Marketing</option>
                                    <option value="seo">SEO Services</option>
                                    <option value="ppc">PPC Advertising</option>
                                    <option value="social-media">Social Media Management</option>
                                    <option value="ai-solutions">AI & Automation Solutions</option>
                                    <option value="other">Other Services</option>
                                </select>
                            </div>

                            <div>
                                <label for="budget" class="block text-sm font-medium text-gray-700 mb-1">Project Budget</label>
                                <select id="budget" name="budget" class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300">
                                    <option value="" disabled selected>Select Budget Range</option>
                                    <option value="less-5k">Less than £5,000</option>
                                    <option value="5k-10k">£5,000 - £10,000</option>
                                    <option value="10k-25k">£10,000 - £25,000</option>
                                    <option value="25k-50k">£25,000 - £50,000</option>
                                    <option value="50k-100k">£50,000 - £100,000</option>
                                    <option value="more-100k">More than £100,000</option>
                                    <option value="not-sure">Not sure yet</option>
                                </select>
                            </div>

                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Project Details</label>
                                <textarea id="message" name="message" rows="5" class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300" placeholder="Tell us about your project, goals, and requirements..."></textarea>
                            </div>

                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="terms" name="terms" type="checkbox" class="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded">
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="terms" class="text-gray-600">I agree to the <a href="#" class="text-primary-600 hover:text-primary-700">Privacy Policy</a> and <a href="#" class="text-primary-600 hover:text-primary-700">Terms of Service</a></label>
                                </div>
                            </div>

                            <div>
                                <button type="submit" class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium rounded-lg text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <span>Submit Inquiry</span>
                                    <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Contact Information -->
                <div>
                    <div class="bg-white rounded-2xl shadow-soft p-8 border border-gray-100 mb-8">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Contact Information</h3>

                        <div class="space-y-6">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-base font-semibold text-gray-900">Phone</h4>
                                    <p class="text-gray-600 mt-1">UK: <a href="tel:+447878361409" class="text-primary-600 hover:text-primary-700">+44 7878 361409</a></p>
                                    <p class="text-gray-600">US: <a href="tel:+17862337886" class="text-primary-600 hover:text-primary-700">+****************</a></p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-base font-semibold text-gray-900">Email</h4>
                                    <p class="text-gray-600 mt-1">General Inquiries: <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-700"><EMAIL></a></p>
                                    <p class="text-gray-600">Support Requests: <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-700"><EMAIL></a></p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-base font-semibold text-gray-900">Address</h4>
                                    <p class="text-gray-600 mt-1">1-A Edmundson Street,</p>
                                    <p class="text-gray-600">Blackburn BB2 1HL,</p>
                                    <p class="text-gray-600">United Kingdom</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-base font-semibold text-gray-900">Business Hours</h4>
                                    <p class="text-gray-600 mt-1">Monday - Friday: 08:30 AM - 06:00 PM (GMT)</p>
                                    <p class="text-gray-600">Emergency Support Available 24/7</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="bg-white rounded-2xl shadow-soft p-8 border border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Connect With Us</h3>

                        <div class="grid grid-cols-4 gap-4">
                            <a href="#" class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group">
                                <i class="fab fa-linkedin text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"></i>
                                <span class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300">LinkedIn</span>
                            </a>
                            <a href="#" class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group">
                                <i class="fab fa-twitter text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"></i>
                                <span class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300">Twitter</span>
                            </a>
                            <a href="#" class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group">
                                <i class="fab fa-facebook text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"></i>
                                <span class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300">Facebook</span>
                            </a>
                            <a href="#" class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group">
                                <i class="fab fa-instagram text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"></i>
                                <span class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300">Instagram</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Google Map -->
<div class="w-full h-96 relative mt-16">
    <iframe
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2362.1054555573354!2d-2.4908899842347944!3d53.748943980074004!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x487b9f6cf9e71a1d%3A0x7f0e9f7a79eb2b31!2sEdmundson%20St%2C%20Blackburn%20BB2%201HL%2C%20UK!5e0!3m2!1sen!2sus!4v1631289726754!5m2!1sen!2sus"
        width="100%"
        height="100%"
        style="border:0;"
        allowfullscreen=""
        loading="lazy"
        class="absolute top-0 left-0 w-full h-full z-0"
    ></iframe>

    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 w-full max-w-md px-4">
        <div class="bg-white rounded-xl shadow-xl p-6 backdrop-blur-md">
            <div class="flex items-center">
                <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">RayDesign Technologies</h3>
                    <p class="text-gray-600 text-sm">1-A Edmundson Street, Blackburn BB2 1HL, United Kingdom</p>
                </div>
            </div>
            <div class="mt-4 flex space-x-2">
                <a href="https://www.google.com/maps/dir//Edmundson+St,+Blackburn+BB2+1HL,+UK/" target="_blank" class="flex-1 inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-300">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                    </svg>
                    Get Directions
                </a>
                <a href="tel:+447878361409" class="inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-lg text-primary-600 bg-primary-50 hover:bg-primary-100 transition-colors duration-300">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    Call
                </a>
            </div>
        </div>
    </div>
</div>
<!-- Footer Section with Comprehensive Information -->
<footer class="bg-gradient-to-b from-gray-50 to-gray-100 pt-20 pb-10 relative overflow-hidden">
    <!-- Background Decoration Elements -->
    <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blobs -->
        <div class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-primary-300/10 to-primary-600/10 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"></div>
        <div class="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-tr from-secondary-300/10 to-secondary-600/10 rounded-full filter blur-3xl opacity-30 animate-float" style="animation-delay: 2s;"></div>

        <!-- SVG Pattern Background -->
        <div class="absolute inset-0 opacity-5">
            <svg width="100%" height="100%">
                <pattern id="footer-dots" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse" patternContentUnits="userSpaceOnUse">
                    <circle id="footer-dot" cx="15" cy="15" r="1" fill="#6d28d9"></circle>
                </pattern>
                <rect x="0" y="0" width="100%" height="100%" fill="url(#footer-dots)"></rect>
            </svg>
        </div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Footer Top Section -->
        <div class="grid grid-cols-1 lg:grid-cols-5 gap-10 pb-10 border-b border-gray-200">
            <!-- Company Information -->
            <div class="lg:col-span-2">
                <a href="/" class="inline-block mb-6">
                    <img class="h-16 w-auto" src="https://i.postimg.cc/cCwXxx3N/Ray-Design-Logo.png" alt="RayDesign Technologies Logo">
                </a>
                <p class="text-gray-600 mb-6 max-w-md">
                    RayDesign Technologies is a future-forging innovation powerhouse that merges design thinking, intelligent automation, full-stack engineering, and marketing science to architect high-impact, human-centered digital ecosystems.
                </p>
                <div class="flex space-x-4 mb-6">
                    <a href="#" class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
                <div class="bg-white rounded-xl shadow-sm p-4 flex items-center">
                    <div class="flex-shrink-0 mr-4">
                        <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center">
                            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Have a question? Call us now</p>
                        <a href="tel:+447878361409" class="text-lg font-semibold text-primary-600 hover:text-primary-700">+44 7878 361409</a>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-lg font-bold text-gray-900 mb-6">Quick Links</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <a href="/about/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            About Us
                        </a>
                    </li>
                    <li>

                    </li>
                    <li>
                        <a href="/portfolio/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            Portfolio
                        </a>
                    </li>

                    <li>
                        <a href="/contact/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            Contact Us
                        </a>
                    </li>
                    <li>
                        <a href="" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            Careers
                        </a>
                    </li>

                </ul>
            </div>

            <!-- Services -->
<div>
    <h3 class="text-lg font-bold text-gray-900 mb-6">Our Services</h3>
    <ul class="space-y-3">
        <li>
            <a href="/services/ai-solutions/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                AI & Automation
            </a>
        </li>
        <li>
            <a href="/services/branding/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                Branding & Identity
            </a>
        </li>
        <li>
            <a href="/services/e-commerce" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                E-Commerce Solutions
            </a>
        </li>
        <li>
            <a href="/services/mobile-apps/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                Mobile App Development
            </a>
        </li>
        <li>
            <a href="/services/ppc/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                Pay-Per-Click (PPC) Ads
            </a>
        </li>
        <li>
            <a href="/services/seo/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                SEO Services
            </a>
        </li>
        <li>
            <a href="/services/social-media/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                Social Media Management
            </a>
        </li>
        <li>
            <a href="/services/ui-ux-design/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                UI/UX Design
            </a>
        </li>
        <li>
            <a href="/services/web-development/" class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2 text-primary-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                Web Development
            </a>
        </li>
    </ul>
</div>


            <!-- Newsletter Signup -->
            <div>
                <h3 class="text-lg font-bold text-gray-900 mb-6">Subscribe to Our Newsletter</h3>
                <p class="text-gray-600 mb-4">Stay updated with our latest news, tips, and special offers.</p>
                <form class="space-y-3">
                    <div>
                        <label for="footer-email" class="sr-only">Email Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                </svg>
                            </div>
                            <input type="email" id="footer-email" name="email" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white" placeholder="Your email address">
                        </div>
                    </div>
                    <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-lg text-white bg-gradient-purple shadow-sm hover:shadow-md transform transition-all duration-300 hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path>
                        </svg>
                        Subscribe
                    </button>
                </form>

                <!-- Industry Badges -->
                <div class="mt-6">
                    <p class="text-sm text-gray-500 mb-3">Recognized By:</p>
                    <div class="flex flex-wrap gap-3">
                        <p>Google Verified Business</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Middle Section - Industries We Serve -->
        <div class="py-10 border-b border-gray-200">
            <h3 class="text-lg font-bold text-gray-900 mb-6">Industries We Serve</h3>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8 gap-4">
                <a href="#" class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group">
                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300">Retail & eCommerce</span>
                </a>

                <a href="#" class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group">
                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300">Healthcare</span>
                </a>

                <a href="#" class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group">
                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300">Finance & Fintech</span>
                </a>

                <a href="#" class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group">
                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300">Education</span>
                </a>

                <a href="#" class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group">
                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300">Real Estate</span>
                </a>

                <a href="#" class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group">
                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300">Travel & Hospitality</span>
                </a>

                <a href="#" class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group">
                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"></path>
                            <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707l-2-2A1 1 0 0015 7h-1z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300">Logistics</span>
                </a>

                <a href="#" class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group">
                    <div class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300">Legal & Compliance</span>
                </a>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="pt-10">
            <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                <div class="mb-6 md:mb-0">
                    <p class="text-sm text-gray-500">&copy; 2023 RayDesign Technologies. All rights reserved.</p>
                </div>

                <div class="flex flex-col md:flex-row gap-4 md:gap-8">
                    <a href="privacy-policy.html" class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300">Privacy Policy</a>
                    <a href="terms-of-service.html" class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300">Terms of Service</a>
                    <a href="cookie-policy.html" class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300">Cookie Policy</a>
                    <a href="sitemap.html" class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300">Sitemap</a>
                </div>
            </div>

            <div class="mt-6 flex flex-col md:flex-row md:justify-between md:items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-xs text-gray-400">RayDesign Technologies is registered in the United Kingdom. Company No: 12345678</p>
                </div>

                <div>
                    <p class="text-xs text-gray-400">Designed and developed with <span class="text-red-500">❤</span> by RayDesign Technologies</p>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<button id="backToTop" class="fixed bottom-6 right-6 w-12 h-12 rounded-full bg-primary-600 text-white shadow-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 flex items-center justify-center transform scale-0 opacity-0">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
</button>

<!-- Cookie Consent -->
<div id="cookieConsent" class="fixed bottom-0 left-0 w-full bg-white shadow-lg transform translate-y-full transition-transform duration-500 z-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="mb-4 md:mb-0 md:mr-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0 mt-0.5">
                        <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">Cookie Notice</h3>
                        <p class="mt-1 text-sm text-gray-600">We use cookies to enhance your browsing experience, analyze site traffic, and personalize content. By clicking "Accept All Cookies", you agree to the storing of cookies on your device.</p>
                        <div class="mt-2">
                            <a href="cookie-policy.html" class="text-sm font-medium text-primary-600 hover:text-primary-700">Learn more about our Cookie Policy</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <button id="cookieSettings" class="inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Cookie Settings
                </button>
                <button id="acceptCookies" class="inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Accept All Cookies
                </button>
                <button id="declineCookies" class="inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Decline Non-Essential
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Initialize AOS Animation Library -->
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<!-- Main JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });

        // Back to Top Button
        const backToTopButton = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('scale-0', 'opacity-0');
                backToTopButton.classList.add('scale-100', 'opacity-100');
            } else {
                backToTopButton.classList.remove('scale-100', 'opacity-100');
                backToTopButton.classList.add('scale-0', 'opacity-0');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Cookie Consent
        const cookieConsent = document.getElementById('cookieConsent');
        const acceptCookies = document.getElementById('acceptCookies');
        const declineCookies = document.getElementById('declineCookies');
        const cookieSettings = document.getElementById('cookieSettings');

        // Check if user has already made a cookie choice
        const cookieChoice = localStorage.getItem('cookieChoice');

        if (!cookieChoice) {
            // Show cookie consent after 2 seconds
            setTimeout(() => {
                cookieConsent.classList.remove('translate-y-full');
                cookieConsent.classList.add('translate-y-0');
            }, 2000);
        }

        acceptCookies.addEventListener('click', () => {
            localStorage.setItem('cookieChoice', 'accepted');
            cookieConsent.classList.remove('translate-y-0');
            cookieConsent.classList.add('translate-y-full');
        });

        declineCookies.addEventListener('click', () => {
            localStorage.setItem('cookieChoice', 'declined');
            cookieConsent.classList.remove('translate-y-0');
            cookieConsent.classList.add('translate-y-full');
        });

        cookieSettings.addEventListener('click', () => {
            // Implement cookie settings modal or redirect to cookie policy page
            window.location.href = 'cookie-policy.html';
        });
    });
</script>
<!-- JavaScript for Animated Counters -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const counters = document.querySelectorAll('.counter');
        const speed = 200;

        const animateCounter = (counter) => {
            const target = +counter.dataset.target;
            let count = 0;
            const inc = target / speed;

            const updateCount = () => {
                if (count < target) {
                    count += inc;
                    counter.innerText = Math.ceil(count);
                    setTimeout(updateCount, 1);
                } else {
                    counter.innerText = target;
                }
            };

            updateCount();
        };

        // Use Intersection Observer to trigger counter animation when visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    animateCounter(counter);
                    observer.unobserve(counter);
                }
            });
        }, { threshold: 0.2 });

        counters.forEach(counter => {
            observer.observe(counter);
        });
    });
</script>
<!-- JavaScript for Services Section -->
<script>
    // Filter functionality for service cards
    document.addEventListener('DOMContentLoaded', function() {
        const filterButtons = document.querySelectorAll('.service-filter-btn');
        const serviceCards = document.querySelectorAll('.service-card');

        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                filterButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-primary-600', 'text-white');
                    btn.classList.add('bg-white', 'text-gray-700');
                });

                // Add active class to clicked button
                button.classList.add('active', 'bg-primary-600', 'text-white');
                button.classList.remove('bg-white', 'text-gray-700');

                const filter = button.getAttribute('data-filter');

                // Show/hide cards based on filter
                serviceCards.forEach(card => {
                    if (filter === 'all' || card.getAttribute('data-category') === filter) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    });
 // Create cursor element
const cursor = document.createElement('div');
cursor.classList.add('custom-cursor');
document.body.appendChild(cursor);

// Add styles
const style = document.createElement('style');
style.innerHTML = `
  body {
    cursor: none;
  }

  .custom-cursor {
    position: fixed;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    pointer-events: none;
    transform: translate(-50%, -50%);
    z-index: 9999;

    /* Blue-Cyan-Purple Gradient */
    background: linear-gradient(
      135deg,
      rgba(0, 255, 255, 0.9) 0%,
      rgba(0, 128, 255, 0.9) 30%,
      rgba(128, 0, 255, 0.9) 60%,
      rgba(255, 0, 255, 0.9) 100%
    );

    box-shadow:
      0 0 6px rgba(0, 255, 255, 0.7),
      0 0 12px rgba(0, 128, 255, 0.6),
      0 0 20px rgba(128, 0, 255, 0.8);

    transition: all 0.2s ease;
    animation: rainbow-rotate 5s linear infinite, pulse-fade 2s ease-in-out infinite;
  }

  @keyframes rainbow-rotate {
    0% {
      filter: hue-rotate(0deg);
    }
    100% {
      filter: hue-rotate(360deg);
    }
  }

  @keyframes pulse-fade {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.08);
      opacity: 0.85;
    }
  }

  .custom-cursor.hollow {
    width: 35px;
    height: 35px;
    background: transparent;
    border: 2px solid rgba(0, 200, 255, 0.8);
    box-shadow:
      0 0 10px rgba(0, 128, 255, 0.7),
      inset 0 0 10px rgba(128, 0, 255, 0.5);
    animation: pulse 1.5s ease-in-out infinite alternate;
  }

  @keyframes pulse {
    0% {
      box-shadow:
        0 0 10px rgba(0, 128, 255, 0.7),
        inset 0 0 10px rgba(128, 0, 255, 0.5);
    }
    100% {
      box-shadow:
        0 0 15px rgba(0, 200, 255, 0.8),
        inset 0 0 15px rgba(181, 0, 255, 0.6);
    }
  }
`;

document.head.appendChild(style);

// Track mouse movement with light smoothing
let mouseX = 0, mouseY = 0;
let cursorX = 0, cursorY = 0;
const smoothing = 0.2;

document.addEventListener('mousemove', (e) => {
  mouseX = e.clientX;
  mouseY = e.clientY;
});

// Check proximity to headings
function checkHeadingProximity() {
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let nearHeading = false;

  headings.forEach(heading => {
    const rect = heading.getBoundingClientRect();

    // Create a slightly larger detection area around the heading
    const expandedRect = {
      left: rect.left - 10,
      right: rect.right + 10,
      top: rect.top - 10,
      bottom: rect.bottom + 10
    };

    // Check if cursor is near the heading
    if (mouseX >= expandedRect.left &&
        mouseX <= expandedRect.right &&
        mouseY >= expandedRect.top &&
        mouseY <= expandedRect.bottom) {
      nearHeading = true;
    }
  });

  // Update cursor appearance based on proximity
  if (nearHeading) {
    cursor.classList.add('hollow');
  } else {
    cursor.classList.remove('hollow');
  }
}

// Animation loop for smooth movement and heading detection
function animate() {
  // Smooth cursor movement
  cursorX += (mouseX - cursorX) * smoothing;
  cursorY += (mouseY - cursorY) * smoothing;

  // Update cursor position
  cursor.style.left = `${cursorX}px`;
  cursor.style.top = `${cursorY}px`;

  // Check if near headings
  checkHeadingProximity();

  // Continue animation
  requestAnimationFrame(animate);
}

// Start animation
animate();

// Hide default cursor when page loads
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    document.body.style.cursor = 'none';
  }, 100);
});
</script>
<script>
(function(){if(!window.chatbase||window.chatbase("getState")!=="initialized"){window.chatbase=(...arguments)=>{if(!window.chatbase.q){window.chatbase.q=[]}window.chatbase.q.push(arguments)};window.chatbase=new Proxy(window.chatbase,{get(target,prop){if(prop==="q"){return target.q}return(...args)=>target(prop,...args)}})}const onLoad=function(){const script=document.createElement("script");script.src="https://www.chatbase.co/embed.min.js";script.id="zo0iKR7_GZSw0On-rhGft";script.domain="www.chatbase.co";document.body.appendChild(script)};if(document.readyState==="complete"){onLoad()}else{window.addEventListener("load",onLoad)}})();
</script>
</body>
</html>
