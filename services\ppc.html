<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      RayDesign Technologies | Elevating Businesses Through Digital Brilliance
    </title>
    <meta
      name="description"
      content="RayDesign Technologies - Your strategic transformation partner merging design thinking, intelligent automation, full-stack engineering, and marketing science to create high-impact digital ecosystems."
    />
    <meta
      name="keywords"
      content="web development, mobile app, UI/UX design, digital marketing, AI solutions, RPA, automation, branding, SEO, PPC, eCommerce, SaaS development"
    />
    <meta name="author" content="RayDesign Technologies" />

    <!-- Open Graph / Social Media Meta Tags -->
    <meta
      property="og:title"
      content="RayDesign Technologies | Digital Innovation Partner"
    />
    <meta
      property="og:description"
      content="Transforming business challenges into seamless digital experiences through innovative technology solutions and strategic digital marketing."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://raydesigntechnologies.com" />
    <meta
      property="og:image"
      content="https://raydesigntechnologies.com/images/og-image.jpg"
    />
    <meta property="og:site_name" content="RayDesign Technologies" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="RayDesign Technologies | Digital Innovation Partner"
    />
    <meta
      name="twitter:description"
      content="Your strategic partner for web development, mobile apps, digital marketing, and AI solutions."
    />
    <meta
      name="twitter:image"
      content="https://raydesigntechnologies.com/images/twitter-card.jpg"
    />

    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5" />
    <meta name="msapplication-TileColor" content="#6b46c1" />
    <meta name="theme-color" content="#6b46c1" />

    <!-- Canonical URL -->
    <meta rel="canonical" href="https://raydesigntechnologies.com" />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&family=Montserrat:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Animate.css -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />

    <!-- AOS - Animate On Scroll -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                50: "#f5f3ff",
                100: "#ede9fe",
                200: "#ddd6fe",
                300: "#c4b5fd",
                400: "#a78bfa",
                500: "#8b5cf6",
                600: "#7c3aed",
                700: "#6d28d9",
                800: "#5b21b6",
                900: "#4c1d95",
                950: "#2e1065",
              },
              secondary: {
                50: "#fdf2f8",
                100: "#fce7f3",
                200: "#fbcfe8",
                300: "#f9a8d4",
                400: "#f472b6",
                500: "#ec4899",
                600: "#db2777",
                700: "#be185d",
                800: "#9d174d",
                900: "#831843",
                950: "#500724",
              },
              dark: "#121212",
              light: "#f8f9fa",
            },
            fontFamily: {
              inter: ["Inter", "sans-serif"],
              poppins: ["Poppins", "sans-serif"],
              montserrat: ["Montserrat", "sans-serif"],
            },
            boxShadow: {
              glass: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
              neon: '0 0 5px theme("colors.primary.400"), 0 0 20px theme("colors.primary.700")',
              soft: "0 10px 50px -12px rgba(0, 0, 0, 0.25)",
            },
            backgroundImage: {
              "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
              "gradient-conic": "conic-gradient(var(--tw-gradient-stops))",
            },
            animation: {
              float: "float 6s ease-in-out infinite",
              "pulse-slow": "pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite",
              "bounce-slow": "bounce 3s infinite",
            },
            keyframes: {
              float: {
                "0%, 100%": { transform: "translateY(0)" },
                "50%": { transform: "translateY(-20px)" },
              },
            },
            borderRadius: {
              xl: "1rem",
              "2xl": "1.5rem",
              "3xl": "2rem",
              "4xl": "3rem",
            },
          },
        },
        variants: {
          extend: {
            opacity: ["group-hover"],
            transform: ["group-hover"],
            scale: ["group-hover"],
            translate: ["group-hover"],
          },
        },
      };
    </script>

    <style>
      /* Custom Styles */
      .bg-glass {
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.18);
      }

      .bg-glass-dark {
        background: rgba(17, 17, 17, 0.75);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.05);
      }

      .text-gradient {
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        background-image: linear-gradient(to right, #7c3aed, #db2777);
      }

      .bg-gradient-purple {
        background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
      }

      .bg-gradient-purple-pink {
        background: linear-gradient(135deg, #5b21b6 0%, #be185d 100%);
      }

      .bg-dots {
        background-image: radial-gradient(#6d28d9 1px, transparent 1px);
        background-size: 20px 20px;
      }

      /* Custom Animations */
      @keyframes glow {
        0%,
        100% {
          box-shadow: 0 0 15px rgba(139, 92, 246, 0.7);
        }
        50% {
          box-shadow: 0 0 30px rgba(139, 92, 246, 0.9);
        }
      }

      .animate-glow {
        animation: glow 3s ease-in-out infinite;
      }

      /* Custom Scrollbar */
      ::-webkit-scrollbar {
        width: 10px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      ::-webkit-scrollbar-thumb {
        background: #6d28d9;
        border-radius: 5px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #5b21b6;
      }

      /* Smooth Scrolling */
      html {
        scroll-behavior: smooth;
      }

      /* Curved Sections */
      .curve-top {
        position: relative;
      }

      .curve-top::before {
        content: "";
        position: absolute;
        top: -50px;
        left: 0;
        width: 100%;
        height: 50px;
        background: inherit;
        border-top-left-radius: 50% 100%;
        border-top-right-radius: 50% 100%;
      }

      .curve-bottom {
        position: relative;
      }

      .curve-bottom::after {
        content: "";
        position: absolute;
        bottom: -50px;
        left: 0;
        width: 100%;
        height: 50px;
        background: inherit;
        border-bottom-left-radius: 50% 100%;
        border-bottom-right-radius: 50% 100%;
      }

      /* Custom Utility Classes */
      .backdrop-blur {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
      }
    </style>
  </head>
  <body class="font-inter antialiased text-gray-800 overflow-x-hidden bg-white">
    <!-- Preloader -->
    <div
      id="preloader"
      class="fixed inset-0 z-50 flex items-center justify-center bg-white"
    >
      <div class="relative w-24 h-24">
        <div
          class="absolute top-0 left-0 w-full h-full rounded-full border-4 border-t-primary-600 border-r-primary-400 border-b-primary-200 border-l-transparent animate-spin"
        ></div>
        <img
          src="https://i.postimg.cc/cCwXxx3N/Ray-Design-Logo.png"
          alt="RayDesign Technologies"
          class="w-16 h-16 object-contain absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        />
      </div>
    </div>

    <!-- Notification Bar -->
    <div
      class="bg-gradient-purple text-white py-2 px-4 text-center relative overflow-hidden"
    >
      <div
        class="animate-marquee whitespace-nowrap flex items-center justify-center"
      >
        <span class="text-sm font-medium mx-4"
          >🎉 Limited Time Offer: Get a FREE 45-minute consultation with our
          experts!
          <a href="#contact" class="underline font-bold ml-2 hover:text-white"
            >Book Now</a
          ></span
        >
        <span class="mx-4">|</span>
        <span class="text-sm font-medium mx-4"
          >🚀 New AI-powered solutions that boost conversion rates by 300%
          <a href="#services" class="underline font-bold ml-2 hover:text-white"
            >Learn More</a
          ></span
        >
        <span class="mx-4">|</span>
        <span class="text-sm font-medium mx-4"
          >💼 Trusted by 500+ businesses worldwide
          <a
            href="#testimonials"
            class="underline font-bold ml-2 hover:text-white"
            >See Testimonials</a
          ></span
        >
      </div>
    </div>

    <!-- Header Navigation -->
    <header
      id="header"
      class="sticky top-0 z-40 w-full transition-all duration-300"
    >
      <nav class="bg-glass border-b border-gray-200 backdrop-blur-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-20">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
              <a href="/" class="flex items-center">
                <img
                  class="h-12 w-auto"
                  src="https://i.postimg.cc/cCwXxx3N/Ray-Design-Logo.png"
                  alt="RayDesign Technologies Logo"
                />
              </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:ml-6 md:flex md:items-center md:space-x-4">
              <a
                href="/"
                class="px-3 py-2 rounded-md text-sm font-medium text-primary-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group"
              >
                Home
                <span
                  class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                ></span>
              </a>
              <a
                href="about.html"
                class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group"
              >
                About Us
                <span
                  class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                ></span>
              </a>

              <!-- Services Dropdown -->
              <div class="relative group">
                <button
                  class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 inline-flex items-center group-hover:text-primary-700"
                >
                  Services
                  <svg
                    class="ml-1 w-4 h-4 transition-transform duration-300 group-hover:rotate-180"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                  <span
                    class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                  ></span>
                </button>

                <!-- Dropdown Menu -->
                <div
                  class="absolute left-0 mt-2 w-72 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform origin-top-left group-hover:translate-y-0 translate-y-2 z-50 border border-gray-100"
                >
                  <div class="p-4 grid grid-cols-1 gap-2">
                    <div class="col-span-1">
                      <h3
                        class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                      >
                        Web & Mobile
                      </h3>
                      <a
                        href="/services/web-development.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-laptop-code mr-3 text-primary-500"></i>
                        Web Development
                      </a>
                      <a
                        href="/services/mobile-apps.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-mobile-alt mr-3 text-primary-500"></i>
                        Mobile Applications
                      </a>
                      <a
                        href="/services/e-commerce.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i
                          class="fas fa-shopping-cart mr-3 text-primary-500"
                        ></i>
                        E-Commerce Solutions
                      </a>
                    </div>
                  </div>

                  <div
                    class="p-4 border-t border-gray-100 grid grid-cols-1 gap-2"
                  >
                    <div class="col-span-1">
                      <h3
                        class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                      >
                        Marketing
                      </h3>
                      <a
                        href="/services/social-media.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-share-alt mr-3 text-primary-500"></i>
                        Social Media Marketing
                      </a>
                      <a
                        href="/services/seo.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-search mr-3 text-primary-500"></i>
                        SEO Services
                      </a>
                      <a
                        href="/services/ppc.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-bullseye mr-3 text-primary-500"></i>
                        PPC Advertising
                      </a>
                    </div>
                  </div>

                  <div
                    class="p-4 border-t border-gray-100 grid grid-cols-1 gap-2"
                  >
                    <div class="col-span-1">
                      <h3
                        class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                      >
                        Design & Innovation
                      </h3>
                      <a
                        href="/services/ui-ux-design.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i
                          class="fas fa-pencil-ruler mr-3 text-primary-500"
                        ></i>
                        UI/UX Design
                      </a>
                      <a
                        href="/services/branding.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-paint-brush mr-3 text-primary-500"></i>
                        Branding & Identity
                      </a>
                    </div>
                  </div>

                  <div
                    class="p-4 border-t border-gray-100 grid grid-cols-1 gap-2"
                  >
                    <div class="col-span-1">
                      <h3
                        class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                      >
                        AI & Automation
                      </h3>
                      <a
                        href="/services/ai-solutions.html"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-700 rounded-md transition-colors duration-150"
                      >
                        <i class="fas fa-robot mr-3 text-primary-500"></i>
                        AI Solutions
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <a
                href="portfolio.html"
                class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group"
              >
                Portfolio
                <span
                  class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                ></span>
              </a>

              <a
                href="contact.html"
                class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-800 transition-all duration-300 relative group"
              >
                Contact
                <span
                  class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"
                ></span>
              </a>
            </div>

            <!-- Action Buttons -->
            <div class="hidden md:flex items-center">
              <a
                href="#contact"
                class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 shadow-md transition-all duration-300 ml-3"
              >
                Get a Quote
              </a>
              <a
                href="tel:+447878361409"
                class="inline-flex items-center justify-center px-4 py-2 border border-primary-600 text-sm font-medium rounded-md text-primary-600 bg-transparent hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 ml-3"
              >
                <i class="fas fa-phone-alt mr-2"></i> Call Us
              </a>
            </div>

            <!-- Mobile menu button -->
            <div class="flex items-center md:hidden">
              <button
                id="mobile-menu-button"
                type="button"
                class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
                aria-expanded="false"
              >
                <span class="sr-only">Open main menu</span>
                <svg
                  id="menu-icon"
                  class="block h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
                <svg
                  id="close-icon"
                  class="hidden h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile menu, show/hide based on menu state -->
        <div id="mobile-menu" class="hidden md:hidden">
          <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white">
            <a
              href="/"
              class="block px-3 py-2 rounded-md text-base font-medium text-primary-700 bg-primary-50"
              >Home</a
            >
            <a
              href="about.html"
              class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >About Us</a
            >

            <!-- Mobile Services Accordion -->
            <div class="relative">
              <button
                id="mobile-services-button"
                class="w-full flex justify-between items-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >
                Services
                <svg
                  id="mobile-services-icon"
                  class="h-5 w-5 transform transition-transform duration-200"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              <div
                id="mobile-services-dropdown"
                class="hidden px-4 py-2 space-y-1"
              >
                <div class="py-2">
                  <h4
                    class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                  >
                    Web & Mobile
                  </h4>
                  <a
                    href="/services/web-development.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >Web Development</a
                  >
                  <a
                    href="/services/mobile-apps.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >Mobile Applications</a
                  >
                  <a
                    href="/services/e-commerce.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >E-Commerce Solutions</a
                  >
                </div>

                <div class="py-2 border-t border-gray-100">
                  <h4
                    class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                  >
                    Marketing
                  </h4>
                  <a
                    href="/services/social-media.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >Social Media Marketing</a
                  >
                  <a
                    href="/services/seo.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >SEO Services</a
                  >
                  <a
                    href="/services/ppc.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >PPC Advertising</a
                  >
                </div>

                <div class="py-2 border-t border-gray-100">
                  <h4
                    class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                  >
                    Design & Innovation
                  </h4>
                  <a
                    href="/services/ui-ux-design.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >UI/UX Design</a
                  >
                  <a
                    href="/services/branding.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >Branding & Identity</a
                  >
                </div>

                <div class="py-2 border-t border-gray-100">
                  <h4
                    class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2"
                  >
                    AI & Automation
                  </h4>
                  <a
                    href="/services/ai-solutions.html"
                    class="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
                    >AI Solutions</a
                  >
                </div>

                <div class="py-2 border-t border-gray-100">
                  <a
                    href="/services.html"
                    class="flex items-center px-3 py-2 text-sm font-medium text-primary-700 hover:text-primary-900"
                  >
                    View all services
                    <svg
                      class="ml-1 w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                      ></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <a href="portfolio.html" class="block px-3 py-2 rounded-md text-base
            font-medium text-gray-700 hover:bg-primary-50
            hover:text-primary-700" Portfolio / >

            <a
              href="contact.html"
              class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >Contact</a
            >
          </div>

          <div class="pt-4 pb-3 border-t border-gray-200">
            <div class="flex items-center px-4">
              <div class="flex-shrink-0">
                <i class="fas fa-headset text-2xl text-primary-600"></i>
              </div>
              <div class="ml-3">
                <div class="text-base font-medium text-gray-800">
                  Need help?
                </div>
                <div class="text-sm font-medium text-gray-500">
                  Our experts are available
                </div>
              </div>
            </div>
            <div class="mt-3 space-y-1 px-2">
              <a
                href="tel:+447878361409"
                class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >
                <i class="fas fa-phone-alt mr-2"></i> +44 7878 361409
              </a>
              <a
                href="mailto:<EMAIL>"
                class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:bg-primary-50 hover:text-primary-700"
              >
                <i class="fas fa-envelope mr-2"></i>
                <EMAIL>
              </a>
              <a
                href="#contact"
                class="block px-3 py-2 rounded-md text-base font-medium bg-primary-600 text-white hover:bg-primary-700 mt-3 text-center"
              >
                Get a Quote
              </a>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <!-- JavaScript for Header Interactions -->
    <script>
      // Handle header background change on scroll
      const header = document.getElementById("header");
      window.addEventListener("scroll", () => {
        if (window.scrollY > 10) {
          header.classList.add("bg-white", "shadow-md");
          header.classList.remove("bg-transparent");
        } else {
          header.classList.remove("bg-white", "shadow-md");
          header.classList.add("bg-transparent");
        }
      });

      // Mobile menu toggle
      const mobileMenuButton = document.getElementById("mobile-menu-button");
      const mobileMenu = document.getElementById("mobile-menu");
      const menuIcon = document.getElementById("menu-icon");
      const closeIcon = document.getElementById("close-icon");

      mobileMenuButton.addEventListener("click", () => {
        mobileMenu.classList.toggle("hidden");
        menuIcon.classList.toggle("hidden");
        closeIcon.classList.toggle("hidden");
      });

      // Mobile services dropdown toggle
      const mobileServicesButton = document.getElementById(
        "mobile-services-button"
      );
      const mobileServicesDropdown = document.getElementById(
        "mobile-services-dropdown"
      );
      const mobileServicesIcon = document.getElementById(
        "mobile-services-icon"
      );

      mobileServicesButton.addEventListener("click", () => {
        mobileServicesDropdown.classList.toggle("hidden");
        mobileServicesIcon.classList.toggle("rotate-180");
      });

      // Preloader
      window.addEventListener("load", () => {
        const preloader = document.getElementById("preloader");
        setTimeout(() => {
          preloader.classList.add("opacity-0");
          setTimeout(() => {
            preloader.style.display = "none";
          }, 500);
        }, 1000);
      });
    </script>

    <!-- Performance Advertising Hero Section -->
    <section class="relative overflow-hidden bg-white py-20 md:py-24 lg:py-28">
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 overflow-hidden z-0">
        <!-- Animated Gradient Blobs -->
        <div
          class="absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-primary-300 to-primary-600 rounded-full filter blur-3xl opacity-20 animate-pulse-slow"
        ></div>
        <div
          class="absolute -bottom-20 -left-20 w-80 h-80 bg-gradient-to-r from-secondary-300 to-secondary-600 rounded-full filter blur-3xl opacity-20 animate-float"
        ></div>

        <!-- Dot Pattern -->
        <div class="absolute inset-0 bg-dots opacity-10"></div>

        <!-- Abstract Shapes -->
        <svg
          class="absolute top-1/4 right-1/3 w-24 h-24 text-primary-100 opacity-50 transform rotate-12 animate-float"
          viewBox="0 0 200 200"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill="currentColor"
            d="M37.5,-65.1C50.2,-58.9,63.1,-52.3,71.6,-41.5C80.1,-30.6,84.2,-15.3,83.6,-0.3C83,14.6,77.8,29.2,69.1,41.2C60.5,53.2,48.4,62.6,35.1,68.3C21.8,74,10.9,76,0.1,75.8C-10.7,75.7,-21.4,73.4,-33.4,69.2C-45.4,65,-58.8,58.8,-69.4,48.5C-80,38.1,-87.9,23.5,-89.4,8.1C-90.9,-7.3,-86,-23.5,-78.4,-38.2C-70.7,-53,-60.2,-66.3,-46.6,-72.1C-33,-78,-16.5,-76.4,-2.3,-72.3C11.9,-68.2,23.9,-61.5,37.5,-65.1Z"
            transform="translate(100 100)"
          />
        </svg>

        <svg
          class="absolute bottom-1/4 left-1/4 w-20 h-20 text-secondary-200 opacity-70 transform -rotate-12 animate-float"
          viewBox="0 0 200 200"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill="currentColor"
            d="M44.3,-76.5C58.4,-71.2,71.3,-61.3,79.4,-48.1C87.5,-34.8,90.8,-18.4,90.3,-2.4C89.8,13.5,85.6,27.1,77.2,38.2C68.9,49.3,56.3,58,43.1,65.5C29.9,73,14.9,79.4,0.3,78.9C-14.3,78.5,-28.7,71.3,-42.7,63.3C-56.7,55.3,-70.4,46.4,-78.8,33.5C-87.2,20.6,-90.3,3.6,-87.4,-12.1C-84.5,-27.8,-75.5,-42.2,-63.9,-53.4C-52.3,-64.5,-38.1,-72.3,-23.6,-76.9C-9.1,-81.5,5.7,-82.9,19.4,-80.8C33.1,-78.7,45.7,-73.2,44.3,-76.5Z"
            transform="translate(100 100)"
          />
        </svg>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <!-- Left Column: Hero Text Content -->
          <div
            class="lg:pr-8"
            data-aos="fade-right"
            data-aos-delay="100"
            data-aos-duration="1000"
          >
            <h5
              class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4 animate__animated animate__fadeInDown"
            >
              <span class="flex items-center">
                <span
                  class="w-2 h-2 rounded-full bg-primary-600 mr-2 animate-pulse"
                ></span>
                Performance Advertising
              </span>
            </h5>

            <h1
              class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-gray-900 mb-6 animate__animated animate__fadeInUp"
            >
              <span class="text-gradient">Maximize</span> Your ROI With
              Data-Driven Campaigns
            </h1>

            <p
              class="text-xl text-gray-600 mb-8 animate__animated animate__fadeInUp animate__delay-1s"
            >
              Strategic paid advertising campaigns across Google, Meta,
              LinkedIn, and other platforms that drive targeted traffic, boost
              conversions, and deliver measurable results for your business.
            </p>

            <div
              class="flex flex-wrap gap-4 mb-8 animate__animated animate__fadeInUp animate__delay-2s"
            >
              <a
                href="#contact"
                class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <span>Start Your Campaign</span>
                <svg
                  class="ml-2 -mr-1 w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </a>

              <a
                href="#platforms"
                class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-primary-700 bg-white border border-primary-200 hover:bg-primary-50 shadow-md hover:shadow-lg transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <span>Explore Platforms</span>
                <svg
                  class="ml-2 -mr-1 w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </a>
            </div>

            <!-- Platform Icons -->
            <div class="animate__animated animate__fadeInUp animate__delay-3s">
              <p class="text-sm font-medium text-gray-500 mb-4">
                STRATEGIC ADVERTISING ACROSS
              </p>
              <div class="flex flex-wrap items-center gap-6">
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 mr-3"
                  >
                    <i class="fab fa-google text-gray-700 text-xl"></i>
                  </div>
                  <span class="font-medium text-gray-700">Google Ads</span>
                </div>

                <div class="flex items-center">
                  <div
                    class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 mr-3"
                  >
                    <i class="fab fa-facebook-f text-gray-700 text-xl"></i>
                  </div>
                  <span class="font-medium text-gray-700">Facebook Ads</span>
                </div>

                <div class="flex items-center">
                  <div
                    class="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 mr-3"
                  >
                    <i class="fab fa-linkedin-in text-gray-700 text-xl"></i>
                  </div>
                  <span class="font-medium text-gray-700">LinkedIn Ads</span>
                </div>
              </div>
            </div>

            <!-- Feature Tags -->
            <div
              class="flex flex-wrap gap-2 mt-8 animate__animated animate__fadeInUp animate__delay-3s"
            >
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
              >
                <svg
                  class="mr-1.5 h-2 w-2 text-green-600"
                  fill="currentColor"
                  viewBox="0 0 8 8"
                >
                  <circle cx="4" cy="4" r="3" />
                </svg>
                Data-Driven Targeting
              </span>
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                <svg
                  class="mr-1.5 h-2 w-2 text-blue-600"
                  fill="currentColor"
                  viewBox="0 0 8 8"
                >
                  <circle cx="4" cy="4" r="3" />
                </svg>
                Conversion Optimization
              </span>
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
              >
                <svg
                  class="mr-1.5 h-2 w-2 text-purple-600"
                  fill="currentColor"
                  viewBox="0 0 8 8"
                >
                  <circle cx="4" cy="4" r="3" />
                </svg>
                Advanced Analytics
              </span>
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
              >
                <svg
                  class="mr-1.5 h-2 w-2 text-yellow-600"
                  fill="currentColor"
                  viewBox="0 0 8 8"
                >
                  <circle cx="4" cy="4" r="3" />
                </svg>
                ROI-Focused Strategy
              </span>
            </div>
          </div>

          <!-- Right Column: Hero Image/Animation -->
          <div
            class="relative"
            data-aos="fade-left"
            data-aos-delay="200"
            data-aos-duration="1000"
          >
            <div
              class="relative z-10 animate__animated animate__zoomIn animate__delay-1s"
            >
              <div
                class="relative bg-white rounded-2xl overflow-hidden shadow-2xl p-3 border border-gray-200"
              >
                <!-- Main Analytics Dashboard Image -->
                <img
                  src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
                  alt="Performance Advertising Dashboard"
                  class="w-full h-auto rounded-xl"
                />

                <!-- Floating Elements -->
                <!-- Conversion Card -->
                <div
                  class="absolute top-4 right-4 bg-white bg-opacity-95 rounded-lg shadow-lg p-3 transform rotate-3 animate-float"
                >
                  <div class="flex items-center">
                    <div
                      class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3"
                    >
                      <i class="fas fa-chart-line text-sm"></i>
                    </div>
                    <div>
                      <p class="text-xs font-bold text-gray-800">Conversions</p>
                      <div class="flex items-center">
                        <p class="text-xs text-green-600 font-bold">+185%</p>
                        <span class="text-xs text-gray-500 ml-1"
                          >vs last month</span
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <!-- ROI Card -->
                <div
                  class="absolute bottom-4 left-4 bg-white bg-opacity-95 rounded-lg shadow-lg p-3 transform -rotate-3 animate-float"
                  style="animation-delay: 1s"
                >
                  <div class="flex items-center">
                    <div
                      class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center text-primary-600 mr-3"
                    >
                      <i class="fas fa-dollar-sign text-sm"></i>
                    </div>
                    <div>
                      <p class="text-xs font-bold text-gray-800">
                        Campaign ROI
                      </p>
                      <div class="flex items-center">
                        <p class="text-xs text-primary-600 font-bold">5.2x</p>
                        <span class="text-xs text-gray-500 ml-1"
                          >return on ad spend</span
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Campaign Performance Graph -->
                <div
                  class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3/4 bg-white bg-opacity-90 rounded-xl shadow-lg p-4 backdrop-blur-sm border border-gray-100"
                >
                  <p class="text-xs font-bold text-gray-700 mb-2">
                    Campaign Performance
                  </p>

                  <!-- Simple Chart -->
                  <div class="h-20 w-full flex items-end space-x-1">
                    <div class="w-1/12 h-5 bg-primary-200 rounded-t"></div>
                    <div class="w-1/12 h-8 bg-primary-300 rounded-t"></div>
                    <div class="w-1/12 h-10 bg-primary-400 rounded-t"></div>
                    <div class="w-1/12 h-12 bg-primary-500 rounded-t"></div>
                    <div class="w-1/12 h-9 bg-primary-400 rounded-t"></div>
                    <div class="w-1/12 h-15 bg-primary-600 rounded-t"></div>
                    <div class="w-1/12 h-14 bg-primary-500 rounded-t"></div>
                    <div class="w-1/12 h-12 bg-primary-500 rounded-t"></div>
                    <div class="w-1/12 h-16 bg-primary-600 rounded-t"></div>
                    <div class="w-1/12 h-19 bg-primary-700 rounded-t"></div>
                    <div class="w-1/12 h-20 bg-primary-700 rounded-t"></div>
                    <div class="w-1/12 h-18 bg-primary-600 rounded-t"></div>
                  </div>

                  <div class="flex justify-between mt-2">
                    <span class="text-xs text-gray-500">Jan</span>
                    <span class="text-xs text-gray-500">Dec</span>
                  </div>
                </div>

                <!-- Platform Icons -->
                <div
                  class="absolute bottom-10 right-10 flex space-x-2 bg-white bg-opacity-80 rounded-lg p-2 backdrop-blur-sm shadow-md"
                >
                  <div
                    class="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center text-red-600"
                  >
                    <i class="fab fa-google text-xs"></i>
                  </div>
                  <div
                    class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600"
                  >
                    <i class="fab fa-facebook-f text-xs"></i>
                  </div>
                  <div
                    class="w-6 h-6 rounded-full bg-blue-200 flex items-center justify-center text-blue-700"
                  >
                    <i class="fab fa-linkedin-in text-xs"></i>
                  </div>
                </div>
              </div>
            </div>

            <!-- Decorative Elements -->
            <div
              class="absolute -bottom-10 -right-10 w-72 h-72 bg-dots rounded-full z-0 opacity-10 border border-primary-200"
            ></div>
            <div
              class="absolute -top-10 -left-10 w-72 h-72 rounded-full border-2 border-dashed border-primary-200 z-0 opacity-30"
            ></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Advertising Platforms Section -->
    <section id="platforms" class="relative overflow-hidden bg-gray-50 py-24">
      <!-- Background Elements -->
      <div class="absolute inset-0 overflow-hidden z-0">
        <!-- Gradient Background -->
        <div
          class="absolute inset-0 bg-gradient-to-br from-primary-50 to-gray-50 opacity-70"
        ></div>

        <!-- Dot Pattern -->
        <div class="absolute inset-0 bg-dots opacity-10"></div>

        <!-- Abstract Shapes -->
        <svg
          class="absolute left-0 bottom-0 text-primary-200 opacity-20 w-40 h-40"
          viewBox="0 0 200 200"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill="currentColor"
            d="M42.8,-73.1C56.9,-66.2,70.7,-57.3,79.4,-44.3C88.1,-31.3,91.7,-15.6,89.9,-1.1C88.1,13.5,80.8,27.1,71.8,39.4C62.8,51.8,52,63,39,69.1C26,75.2,10.8,76.3,-3.9,81.3C-18.7,86.3,-33.3,95.3,-45.8,94.2C-58.3,93.1,-68.7,82,-73.8,68.5C-78.9,55,-78.9,39.1,-80.4,24.5C-81.9,9.9,-85,-3.5,-82.4,-15.9C-79.9,-28.3,-71.6,-39.7,-61,-49C-50.4,-58.3,-37.4,-65.6,-24.2,-72.8C-11,-79.9,2.3,-87,15.4,-85.8C28.5,-84.7,41.4,-75.3,42.8,-73.1Z"
            transform="translate(100 100)"
          />
        </svg>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="max-w-3xl mx-auto text-center mb-16" data-aos="fade-up">
          <h5
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
          >
            <span class="flex items-center justify-center">
              <span
                class="w-2 h-2 rounded-full bg-primary-600 mr-2 animate-pulse"
              ></span>
              Strategic Advertising
            </span>
          </h5>

          <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
            Multi-Channel <span class="text-gradient">Campaign Strategies</span>
          </h2>

          <p class="text-lg text-gray-600">
            We create and manage high-performing paid advertising campaigns
            across the most effective platforms, tailored to your unique
            business objectives and target audience.
          </p>
        </div>

        <!-- Platforms Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
          <!-- Google Ads Card -->
          <div class="relative group" data-aos="fade-up" data-aos-delay="100">
            <div
              class="absolute inset-0 bg-gradient-to-r from-primary-500 to-red-500 rounded-2xl transform transition-all duration-500 group-hover:scale-[0.98] opacity-50 blur-lg"
            ></div>
            <div
              class="relative bg-white rounded-2xl shadow-soft p-8 border border-gray-100 hover:shadow-lg transition-all duration-300 transform group-hover:scale-[0.98]"
            >
              <div
                class="w-16 h-16 rounded-2xl bg-red-50 flex items-center justify-center text-red-500 mb-6 transform group-hover:rotate-6 transition-transform duration-300"
              >
                <i class="fab fa-google text-3xl"></i>
              </div>

              <h3
                class="text-xl font-bold text-gray-900 mb-4 group-hover:text-primary-700 transition-colors duration-300"
              >
                Google Ads
              </h3>

              <p class="text-gray-600 mb-6">
                Capture high-intent traffic and drive conversions with strategic
                Google Search, Display, Video, and Shopping campaigns optimized
                for maximum ROI.
              </p>

              <div class="space-y-3 mb-8">
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Search campaigns targeting high-intent keywords</span
                  >
                </div>
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Retargeting & display network campaigns</span
                  >
                </div>
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Performance-based shopping ads</span
                  >
                </div>
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600">YouTube video advertising</span>
                </div>
              </div>

              <div class="pt-4 border-t border-gray-100">
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500"
                    >Average ROI:</span
                  >
                  <span class="text-sm font-bold text-primary-600">4.8x</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Facebook Ads Card -->
          <div class="relative group" data-aos="fade-up" data-aos-delay="200">
            <div
              class="absolute inset-0 bg-gradient-to-r from-primary-500 to-blue-500 rounded-2xl transform transition-all duration-500 group-hover:scale-[0.98] opacity-50 blur-lg"
            ></div>
            <div
              class="relative bg-white rounded-2xl shadow-soft p-8 border border-gray-100 hover:shadow-lg transition-all duration-300 transform group-hover:scale-[0.98]"
            >
              <div
                class="w-16 h-16 rounded-2xl bg-blue-50 flex items-center justify-center text-blue-600 mb-6 transform group-hover:rotate-6 transition-transform duration-300"
              >
                <i class="fab fa-facebook-f text-3xl"></i>
              </div>

              <h3
                class="text-xl font-bold text-gray-900 mb-4 group-hover:text-primary-700 transition-colors duration-300"
              >
                Facebook & Instagram Ads
              </h3>

              <p class="text-gray-600 mb-6">
                Engage your target audience with visually compelling social
                media campaigns that drive brand awareness, engagement, and
                conversions across Meta platforms.
              </p>

              <div class="space-y-3 mb-8">
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Precision audience targeting & lookalike audiences</span
                  >
                </div>
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Carousel, video & story ad formats</span
                  >
                </div>
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Dynamic product ads & catalog campaigns</span
                  >
                </div>
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Engagement & conversion-focused campaigns</span
                  >
                </div>
              </div>

              <div class="pt-4 border-t border-gray-100">
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500"
                    >Average ROI:</span
                  >
                  <span class="text-sm font-bold text-primary-600">5.2x</span>
                </div>
              </div>
            </div>
          </div>

          <!-- LinkedIn Ads Card -->
          <div class="relative group" data-aos="fade-up" data-aos-delay="300">
            <div
              class="absolute inset-0 bg-gradient-to-r from-primary-500 to-blue-700 rounded-2xl transform transition-all duration-500 group-hover:scale-[0.98] opacity-50 blur-lg"
            ></div>
            <div
              class="relative bg-white rounded-2xl shadow-soft p-8 border border-gray-100 hover:shadow-lg transition-all duration-300 transform group-hover:scale-[0.98]"
            >
              <div
                class="w-16 h-16 rounded-2xl bg-blue-100 flex items-center justify-center text-blue-700 mb-6 transform group-hover:rotate-6 transition-transform duration-300"
              >
                <i class="fab fa-linkedin-in text-3xl"></i>
              </div>

              <h3
                class="text-xl font-bold text-gray-900 mb-4 group-hover:text-primary-700 transition-colors duration-300"
              >
                LinkedIn Ads
              </h3>

              <p class="text-gray-600 mb-6">
                Reach decision-makers and professionals with targeted B2B
                campaigns that leverage LinkedIn's powerful professional
                targeting capabilities.
              </p>

              <div class="space-y-3 mb-8">
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Industry, job title & company size targeting</span
                  >
                </div>
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Sponsored content & InMail campaigns</span
                  >
                </div>
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Lead generation form campaigns</span
                  >
                </div>
                <div class="flex items-start">
                  <svg
                    class="w-5 h-5 text-primary-500 mr-2 mt-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span class="text-gray-600"
                    >Account-based marketing solutions</span
                  >
                </div>
              </div>

              <div class="pt-4 border-t border-gray-100">
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-500"
                    >Average ROI:</span
                  >
                  <span class="text-sm font-bold text-primary-600">6.1x</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Process Overview -->
        <div
          class="mt-24 bg-white rounded-2xl shadow-soft p-8 border border-gray-100"
          data-aos="fade-up"
        >
          <div class="max-w-3xl mx-auto text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-6">
              Our <span class="text-gradient">Data-Driven</span> Approach
            </h2>

            <p class="text-lg text-gray-600">
              We follow a systematic, results-focused methodology to create,
              optimize, and scale your advertising campaigns across all
              platforms.
            </p>
          </div>

          <!-- Process Steps -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Step 1 -->
            <div class="relative">
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center text-primary-600 mb-4"
              >
                <span class="text-xl font-bold">1</span>
              </div>
              <h3 class="text-lg font-bold text-gray-900 mb-2">
                Research & Strategy
              </h3>
              <p class="text-gray-600 text-sm">
                Comprehensive audience analysis, competitive research, and
                platform selection to build a strategic campaign foundation.
              </p>

              <!-- Connector Line (Desktop) -->
              <div
                class="hidden md:block absolute top-6 left-full w-full h-0.5 bg-primary-100 transform -translate-x-6"
              ></div>
            </div>

            <!-- Step 2 -->
            <div class="relative">
              <div
                class="w-12 h-12 rounded-full bg-primary-200 flex items-center justify-center text-primary-700 mb-4"
              >
                <span class="text-xl font-bold">2</span>
              </div>
              <h3 class="text-lg font-bold text-gray-900 mb-2">
                Campaign Creation
              </h3>
              <p class="text-gray-600 text-sm">
                Development of compelling ad creative, precise audience
                targeting, and conversion-optimized landing pages.
              </p>

              <!-- Connector Line (Desktop) -->
              <div
                class="hidden md:block absolute top-6 left-full w-full h-0.5 bg-primary-200 transform -translate-x-6"
              ></div>
            </div>

            <!-- Step 3 -->
            <div class="relative">
              <div
                class="w-12 h-12 rounded-full bg-primary-300 flex items-center justify-center text-primary-800 mb-4"
              >
                <span class="text-xl font-bold">3</span>
              </div>
              <h3 class="text-lg font-bold text-gray-900 mb-2">
                Optimization & Testing
              </h3>
              <p class="text-gray-600 text-sm">
                Continuous A/B testing, performance analysis, and data-driven
                adjustments to maximize campaign effectiveness.
              </p>

              <!-- Connector Line (Desktop) -->
              <div
                class="hidden md:block absolute top-6 left-full w-full h-0.5 bg-primary-300 transform -translate-x-6"
              ></div>
            </div>

            <!-- Step 4 -->
            <div>
              <div
                class="w-12 h-12 rounded-full bg-primary-400 flex items-center justify-center text-white mb-4"
              >
                <span class="text-xl font-bold">4</span>
              </div>
              <h3 class="text-lg font-bold text-gray-900 mb-2">
                Scaling & Reporting
              </h3>
              <p class="text-gray-600 text-sm">
                Strategic scaling of successful campaigns, comprehensive
                reporting, and ongoing refinement for sustained growth.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Advertising Results Section -->
    <section class="relative bg-gradient-purple-pink py-20 overflow-hidden">
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 overflow-hidden">
        <!-- Overlay Patterns -->
        <div
          class="absolute inset-0 opacity-10"
          style="
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzRjMC0yLjIgMS44LTQgNC00czQgMS44IDQgNC0xLjggNC00IDQtNC0xLjgtNC00eiIvPjxwYXRoIGQ9Ik0xNiA0YzAtMi4yIDEuOC00IDQtNHM0IDEuOCA0IDQtMS44IDQtNCA0LTQtMS44LTQtNHoiLz48cGF0aCBkPSJNMTYgNDRjMC0yLjIgMS44LTQgNC00czQgMS44IDQgNC0xLjggNC00IDQtNC0xLjgtNC00eiIvPjwvZz48L2c+PC9zdmc+');
          "
        ></div>

        <!-- Gradient Overlay -->
        <div
          class="absolute inset-0 bg-gradient-to-br from-primary-700 to-secondary-700 opacity-90"
        ></div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="max-w-4xl mx-auto text-center mb-16" data-aos="fade-up">
          <h5
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-white uppercase rounded-full bg-white bg-opacity-20 mb-4"
          >
            <span class="flex items-center justify-center">
              <span
                class="w-2 h-2 rounded-full bg-white mr-2 animate-pulse"
              ></span>
              Campaign Success
            </span>
          </h5>

          <h2 class="text-3xl sm:text-4xl font-bold text-white mb-6">
            Delivering Measurable Results
          </h2>

          <p class="text-lg text-white text-opacity-90">
            Our performance advertising strategies consistently deliver
            exceptional results across platforms, driving traffic, leads, and
            revenue growth for businesses of all sizes.
          </p>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center mb-16">
          <!-- Stat 1 -->
          <div data-aos="fade-up" data-aos-delay="100">
            <div
              class="bg-white bg-opacity-10 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2"
            >
              <div
                class="w-12 h-12 mx-auto rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white mb-4"
              >
                <i class="fas fa-mouse-pointer text-xl"></i>
              </div>

              <div class="text-5xl font-bold text-white mb-2">
                <span class="counter" data-target="320">0</span>
                <span>%</span>
              </div>

              <p class="text-white text-opacity-90">
                Increase in Click-Through Rate
              </p>
            </div>
          </div>

          <!-- Stat 2 -->
          <div data-aos="fade-up" data-aos-delay="200">
            <div
              class="bg-white bg-opacity-10 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2"
            >
              <div
                class="w-12 h-12 mx-auto rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white mb-4"
              >
                <i class="fas fa-chart-line text-xl"></i>
              </div>

              <div class="text-5xl font-bold text-white mb-2">
                <span class="counter" data-target="215">0</span>
                <span>%</span>
              </div>

              <p class="text-white text-opacity-90">Conversion Rate Growth</p>
            </div>
          </div>

          <!-- Stat 3 -->
          <div data-aos="fade-up" data-aos-delay="300">
            <div
              class="bg-white bg-opacity-10 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2"
            >
              <div
                class="w-12 h-12 mx-auto rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white mb-4"
              >
                <i class="fas fa-dollar-sign text-xl"></i>
              </div>

              <div class="text-5xl font-bold text-white mb-2">
                <span class="counter" data-target="5.4">0</span>
                <span>x</span>
              </div>

              <p class="text-white text-opacity-90">Average ROAS</p>
            </div>
          </div>

          <!-- Stat 4 -->
          <div data-aos="fade-up" data-aos-delay="400">
            <div
              class="bg-white bg-opacity-10 rounded-2xl p-8 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2"
            >
              <div
                class="w-12 h-12 mx-auto rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white mb-4"
              >
                <i class="fas fa-percentage text-xl"></i>
              </div>

              <div class="text-5xl font-bold text-white mb-2">
                <span class="counter" data-target="42">0</span>
                <span>%</span>
              </div>

              <p class="text-white text-opacity-90">
                Cost Per Acquisition Reduction
              </p>
            </div>
          </div>
        </div>

        <!-- Client Success Story -->
        <div
          class="bg-white bg-opacity-10 rounded-2xl overflow-hidden backdrop-blur-sm border border-white border-opacity-20"
          data-aos="fade-up"
          data-aos-delay="500"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-0">
            <!-- Image Side -->
            <div class="relative h-full">
              <img
                src="https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                alt="Advertising Case Study"
                class="w-full h-full object-cover"
              />

              <!-- Overlay with ad examples -->
              <div
                class="absolute inset-0 bg-gradient-to-t from-primary-900 to-transparent opacity-70"
              ></div>

              <!-- Platform Mock-Ups -->
              <div
                class="absolute top-1/4 left-1/2 transform -translate-x-1/2 w-3/4 bg-white rounded-lg shadow-lg overflow-hidden"
              >
                <div class="h-2 bg-gray-100 flex items-center px-1 py-3">
                  <div class="w-2 h-2 rounded-full bg-red-500 mr-1"></div>
                  <div class="w-2 h-2 rounded-full bg-yellow-500 mr-1"></div>
                  <div class="w-2 h-2 rounded-full bg-green-500"></div>
                </div>
                <div class="p-2">
                  <div class="w-full h-1 bg-blue-100 mb-1 rounded"></div>
                  <div class="w-3/4 h-1 bg-blue-100 mb-1 rounded"></div>
                  <div class="w-1/2 h-1 bg-blue-100 rounded"></div>
                </div>
              </div>

              <!-- Performance Chart -->
              <div
                class="absolute bottom-8 right-8 bg-white p-2 rounded-lg shadow-lg"
              >
                <div class="w-32 h-20">
                  <svg
                    viewBox="0 0 100 50"
                    preserveAspectRatio="none"
                    class="w-full h-full"
                  >
                    <path
                      d="M0,50 L10,45 L20,48 L30,40 L40,42 L50,35 L60,30 L70,25 L80,20 L90,15 L100,5"
                      stroke="#7c3aed"
                      stroke-width="2"
                      fill="none"
                    ></path>
                    <path
                      d="M0,50 L10,45 L20,48 L30,40 L40,42 L50,35 L60,30 L70,25 L80,20 L90,15 L100,5"
                      stroke="transparent"
                      stroke-width="0"
                      fill="url(#performance-gradient)"
                      opacity="0.2"
                    ></path>
                    <defs>
                      <linearGradient
                        id="performance-gradient"
                        x1="0%"
                        y1="100%"
                        x2="0%"
                        y2="0%"
                      >
                        <stop offset="0%" stop-color="#7c3aed" />
                        <stop offset="100%" stop-color="#db2777" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Content Side -->
            <div class="p-8 md:p-10 flex flex-col justify-center">
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white mr-4"
                >
                  <i class="fas fa-store text-xl"></i>
                </div>
                <h3 class="text-white font-bold text-2xl">
                  LuxeStyle Boutique
                </h3>
              </div>

              <p class="text-white text-opacity-90 mb-6">
                A premium fashion retailer that transformed their online sales
                with our multi-channel advertising strategy, focusing on highly
                targeted Google Shopping ads and Instagram campaigns.
              </p>

              <!-- Results -->
              <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-white bg-opacity-10 rounded-lg p-4">
                  <p class="text-sm text-white text-opacity-70">ROAS</p>
                  <p class="text-2xl font-bold text-white">6.8x</p>
                </div>

                <div class="bg-white bg-opacity-10 rounded-lg p-4">
                  <p class="text-sm text-white text-opacity-70">
                    CPA Reduction
                  </p>
                  <p class="text-2xl font-bold text-white">-62%</p>
                </div>
              </div>

              <!-- Client Quote -->
              <div class="mb-6">
                <p class="text-white text-opacity-90 italic text-sm">
                  "RayDesign Technologies transformed our advertising strategy
                  with a data-driven approach that not only increased our sales
                  but significantly improved our return on ad spend. Their
                  multi-channel campaigns consistently outperform our previous
                  efforts."
                </p>
                <p class="text-white font-medium mt-2">
                  — Marketing Director, LuxeStyle Boutique
                </p>
              </div>

              <!-- Campaign Platforms -->
              <div>
                <p class="text-white text-opacity-70 text-sm mb-2">
                  Platforms Used:
                </p>
                <div class="flex flex-wrap gap-2">
                  <span
                    class="inline-block px-3 py-1 text-xs font-medium bg-white bg-opacity-20 text-white rounded-full"
                    >Google Shopping</span
                  >
                  <span
                    class="inline-block px-3 py-1 text-xs font-medium bg-white bg-opacity-20 text-white rounded-full"
                    >Instagram Ads</span
                  >
                  <span
                    class="inline-block px-3 py-1 text-xs font-medium bg-white bg-opacity-20 text-white rounded-full"
                    >Facebook Remarketing</span
                  >
                  <span
                    class="inline-block px-3 py-1 text-xs font-medium bg-white bg-opacity-20 text-white rounded-full"
                    >Display Network</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- CTA -->
        <div class="mt-16 text-center" data-aos="fade-up">
          <a
            href="#contact"
            class="inline-flex items-center px-8 py-4 text-base font-medium rounded-full text-primary-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
          >
            <span>Boost Your Ad Performance</span>
            <svg
              class="ml-2 -mr-1 w-5 h-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </a>
        </div>
      </div>

      <!-- Counter Animation Script -->
      <script>
        document.addEventListener("DOMContentLoaded", function () {
          const counters = document.querySelectorAll(".counter");
          const speed = 200;

          const observer = new IntersectionObserver(
            (entries) => {
              entries.forEach((entry) => {
                if (entry.isIntersecting) {
                  const counter = entry.target;
                  const target = parseFloat(
                    counter.getAttribute("data-target")
                  );
                  let count = 0;
                  const decimal = String(target).includes(".") ? 1 : 0;

                  const updateCount = () => {
                    const increment = target / speed;
                    if (count < target) {
                      count += increment;
                      counter.innerText = count.toFixed(decimal);
                      setTimeout(updateCount, 1);
                    } else {
                      counter.innerText = target.toFixed(decimal);
                    }
                  };
                  updateCount();
                  observer.unobserve(counter);
                }
              });
            },
            { threshold: 0.5 }
          );

          counters.forEach((counter) => {
            observer.observe(counter);
          });
        });
      </script>
    </section>
    <!-- About Us Section with Company Story -->
    <section id="about-us" class="py-20 bg-white relative overflow-hidden">
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blob -->
        <div
          class="absolute -top-32 -right-32 w-96 h-96 bg-gradient-to-br from-primary-100 to-primary-300 rounded-full filter blur-3xl opacity-30 animate-float"
        ></div>
        <div
          class="absolute -bottom-32 -left-32 w-96 h-96 bg-gradient-to-tr from-secondary-100 to-secondary-300 rounded-full filter blur-3xl opacity-30 animate-float"
          style="animation-delay: 2s"
        ></div>

        <!-- SVG Pattern Background -->
        <div class="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <pattern
              id="pattern-circles"
              x="0"
              y="0"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <circle
                id="pattern-circle"
                cx="20"
                cy="20"
                r="3.5"
                fill="#6d28d9"
              ></circle>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#pattern-circles)"
            ></rect>
          </svg>
        </div>

        <!-- SVG Wave Shape -->
        <svg
          class="absolute top-0 left-0 w-full"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
        >
          <path
            fill="rgba(237, 233, 254, 0.5)"
            fill-opacity="1"
            d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,85.3C672,75,768,85,864,112C960,139,1056,181,1152,181.3C1248,181,1344,139,1392,117.3L1440,96L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z"
          ></path>
        </svg>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- Left Column: Company Story -->
          <div
            class="lg:pr-10"
            data-aos="fade-right"
            data-aos-delay="100"
            data-aos-duration="1000"
          >
            <span
              class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
            >
              Our Story
            </span>

            <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-6">
              Not Just a Digital Agency—A
              <span class="text-gradient">Future-Forging</span> Innovation
              Powerhouse
            </h2>

            <p class="text-lg text-gray-600 mb-6 leading-relaxed">
              At RayDesign Technologies, we are a strategic transformation
              partner that merges the best of design thinking, intelligent
              automation, full-stack engineering, and marketing science to
              architect high-impact, human-centered digital ecosystems.
            </p>

            <p class="text-lg text-gray-600 mb-8 leading-relaxed">
              Our ethos is rooted in transforming complex, chaotic business
              challenges into seamless, scalable, and emotionally engaging
              digital experiences—fueling business growth, operational
              efficiency, and brand transcendence in a fast-paced, hyper-digital
              world.
            </p>

            <!-- Company Values -->
            <div class="space-y-6 mb-8">
              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div
                    class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-primary-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M5.05 3.636a1 1 0 010 1.414 7 7 0 000 9.9 1 1 0 11-1.414 1.414 9 9 0 010-12.728 1 1 0 011.414 0zm9.9 0a1 1 0 011.414 0 9 9 0 010 12.728 1 1 0 11-1.414-1.414 7 7 0 000-9.9 1 1 0 010-1.414zM7.879 6.464a1 1 0 010 1.414 3 3 0 000 4.243 1 1 0 11-1.415 1.414 5 5 0 010-7.07 1 1 0 011.415 0zm4.242 0a1 1 0 011.415 0 5 5 0 010 7.072 1 1 0 01-1.415-1.415 3 3 0 000-4.242 1 1 0 010-1.415z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900">
                    Innovation at Core
                  </h3>
                  <p class="text-gray-600">
                    We constantly push boundaries, embracing emerging
                    technologies and methodologies to deliver cutting-edge
                    solutions.
                  </p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div
                    class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-primary-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900">
                    Human-Centered Approach
                  </h3>
                  <p class="text-gray-600">
                    We design with empathy, putting users at the center of
                    everything we create to build meaningful digital
                    experiences.
                  </p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <div
                    class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-primary-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
                      ></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900">
                    Collaborative Excellence
                  </h3>
                  <p class="text-gray-600">
                    We work closely with our clients as true partners, combining
                    our expertise with their industry knowledge.
                  </p>
                </div>
              </div>
            </div>

            <!-- CTA Button -->
            <a
              href="#contact"
              class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span>Partner With Us</span>
              <svg
                class="ml-2 -mr-1 w-5 h-5"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </a>
          </div>

          <!-- Right Column: Image Collage -->
          <div
            class="lg:pl-10 relative"
            data-aos="fade-left"
            data-aos-delay="300"
            data-aos-duration="1000"
          >
            <div class="relative h-[500px] w-full">
              <!-- Main Image -->
              <div
                class="absolute top-0 right-0 w-[75%] h-[65%] rounded-2xl overflow-hidden shadow-xl z-20 animate-float"
                style="animation-delay: 0.5s"
              >
                <img
                  src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?auto=format&fit=crop&w=800&q=80"
                  alt="RayDesign Team Collaboration"
                  class="w-full h-full object-cover"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-tr from-primary-900/40 to-transparent"
                ></div>
              </div>

              <!-- Secondary Image -->
              <div
                class="absolute bottom-0 left-0 w-[65%] h-[55%] rounded-2xl overflow-hidden shadow-xl z-10 animate-float"
                style="animation-delay: 1s"
              >
                <img
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?auto=format&fit=crop&w=800&q=80"
                  alt="RayDesign Office Culture"
                  class="w-full h-full object-cover"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-tr from-primary-900/40 to-transparent"
                ></div>
              </div>

              <!-- Decorative Elements -->
              <div
                class="absolute top-[40%] left-[20%] w-16 h-16 rounded-full bg-primary-100 z-30 animate-pulse flex items-center justify-center"
              >
                <svg
                  class="w-8 h-8 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>

              <!-- Stats Card -->
              <div
                class="absolute -bottom-6 right-10 bg-white rounded-xl shadow-lg p-4 z-30"
              >
                <div class="grid grid-cols-2 gap-6">
                  <div class="text-center">
                    <h4 class="text-3xl font-bold text-primary-600">8+</h4>
                    <p class="text-gray-600 text-sm">Years Experience</p>
                  </div>
                  <div class="text-center">
                    <h4 class="text-3xl font-bold text-primary-600">500+</h4>
                    <p class="text-gray-600 text-sm">Happy Clients</p>
                  </div>
                </div>
              </div>

              <!-- Pattern Background -->
              <div
                class="absolute -bottom-4 -right-4 w-32 h-32 bg-dots opacity-20 z-0"
              ></div>
            </div>
          </div>
        </div>

        <!-- Mission & Vision Section -->
        <div class="mt-24 grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Mission Card -->
          <div
            class="bg-white rounded-2xl p-8 shadow-soft border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <div class="flex items-center mb-6">
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mr-4"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-gray-900">Our Mission</h3>
            </div>
            <p class="text-gray-600 leading-relaxed mb-6">
              To empower businesses through innovative digital solutions that
              solve complex challenges, enhance user experiences, and drive
              sustainable growth. We are committed to delivering excellence
              through a perfect blend of creativity, technology, and strategic
              thinking.
            </p>
            <div class="pt-4 border-t border-gray-100">
              <div class="flex items-center">
                <img
                  src="https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?auto=format&fit=crop&w=100&h=100&q=80"
                  alt="CEO"
                  class="w-10 h-10 rounded-full object-cover mr-3"
                />
              </div>
            </div>
          </div>

          <!-- Vision Card -->
          <div
            class="bg-white rounded-2xl p-8 shadow-soft border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <div class="flex items-center mb-6">
              <div
                class="w-12 h-12 rounded-full bg-secondary-100 flex items-center justify-center mr-4"
              >
                <svg
                  class="w-6 h-6 text-secondary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                  <path
                    fill-rule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-gray-900">Our Vision</h3>
            </div>
            <p class="text-gray-600 leading-relaxed mb-6">
              To be the global leader in digital innovation, recognized for
              transforming businesses through cutting-edge technology solutions.
              We envision a world where every business, regardless of size, has
              access to the tools and expertise needed to thrive in the digital
              landscape.
            </p>
            <div class="pt-4 border-t border-gray-100">
              <div class="flex items-center">
                <img
                  src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?auto=format&fit=crop&w=100&h=100&q=80"
                  alt="CTO"
                  class="w-10 h-10 rounded-full object-cover mr-3"
                />
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Process Section -->
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 pt-24 relative z-10">
        <div class="text-center max-w-3xl mx-auto mb-16" data-aos="fade-up">
          <span
            class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
          >
            Our Process
          </span>
          <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">
            Our Process of <span class="text-gradient">Excellence</span>
          </h2>
          <p class="text-xl text-gray-600">
            At RayDesign Technologies, we follow a multi-disciplinary and
            precision-engineered process that ensures every digital solution is
            not only beautiful and intelligent—but strategically aligned with
            measurable business outcomes.
          </p>
        </div>

        <!-- Process Timeline -->
        <div class="relative">
          <!-- Vertical Line -->
          <div
            class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-primary-400 to-primary-600 rounded-full hidden md:block"
          ></div>

          <div class="space-y-12">
            <!-- Process Step 1 -->
            <div class="relative" data-aos="fade-up" data-aos-delay="100">
              <div
                class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"
              ></div>

              <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                  >
                    <h3
                      class="text-xl font-bold text-gray-900 mb-3 flex items-center justify-end"
                    >
                      <span>Discovery & Strategic Alignment</span>
                      <div
                        class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center ml-4 flex-shrink-0"
                      >
                        <span class="text-primary-600 font-bold">1</span>
                      </div>
                    </h3>
                    <p class="text-gray-600 mb-4">
                      We begin with a comprehensive discovery phase to
                      understand your business objectives, target audience, and
                      competitive landscape.
                    </p>
                    <ul class="space-y-2 text-right">
                      <li class="flex items-center justify-end">
                        <span>Deep-dive brand immersion workshops</span>
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                      <li class="flex items-center justify-end">
                        <span
                          >Industry benchmarking and competitor gap
                          analysis</span
                        >
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                      <li class="flex items-center justify-end">
                        <span>Stakeholder interviews and KPI alignment</span>
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="md:w-1/2 md:pl-12">
                  <div class="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src="https://images.unsplash.com/photo-1552664730-d307ca884978?auto=format&fit=crop&w=800&q=80"
                      alt="Discovery Phase"
                      class="w-full h-64 object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Step 2 -->
            <div class="relative" data-aos="fade-up" data-aos-delay="200">
              <div
                class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"
              ></div>

              <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0 md:order-2">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                  >
                    <h3
                      class="text-xl font-bold text-gray-900 mb-3 flex items-center"
                    >
                      <div
                        class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-4 flex-shrink-0"
                      >
                        <span class="text-primary-600 font-bold">2</span>
                      </div>
                      <span>Ideation & Prototyping</span>
                    </h3>
                    <p class="text-gray-600 mb-4">
                      We transform insights into actionable concepts, rapidly
                      prototyping solutions to validate ideas before full
                      development.
                    </p>
                    <ul class="space-y-2">
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Design sprints to rapidly visualize core
                          functionalities</span
                        >
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Wireframing, low-fidelity UX mapping, and journey
                          visualization</span
                        >
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Stakeholder validation loops for early feedback</span
                        >
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="md:w-1/2 md:pl-12 md:order-1">
                  <div class="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src="https://images.unsplash.com/photo-1542744173-05336fcc7ad4?auto=format&fit=crop&w=800&q=80"
                      alt="Ideation Phase"
                      class="w-full h-64 object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Step 3 -->
            <div class="relative" data-aos="fade-up" data-aos-delay="300">
              <div
                class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"
              ></div>

              <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                  >
                    <h3
                      class="text-xl font-bold text-gray-900 mb-3 flex items-center justify-end"
                    >
                      <span>Engineering & Implementation</span>
                      <div
                        class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center ml-4 flex-shrink-0"
                      >
                        <span class="text-primary-600 font-bold">3</span>
                      </div>
                    </h3>
                    <p class="text-gray-600 mb-4">
                      Our engineering team brings concepts to life using the
                      latest technologies and best practices for robust,
                      scalable solutions.
                    </p>
                    <ul class="space-y-2 text-right">
                      <li class="flex items-center justify-end">
                        <span>Agile sprint cycles with CI/CD pipelines</span>
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                      <li class="flex items-center justify-end">
                        <span
                          >Secure codebases, microservices architecture, and
                          API-first development</span
                        >
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                      <li class="flex items-center justify-end">
                        <span
                          >QA automation, manual testing, and continuous
                          optimization</span
                        >
                        <svg
                          class="w-5 h-5 text-primary-500 ml-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="md:w-1/2 md:pl-12">
                  <div class="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src="https://images.unsplash.com/photo-1551434678-e076c223a692?auto=format&fit=crop&w=800&q=80"
                      alt="Engineering Phase"
                      class="w-full h-64 object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Step 4 -->
            <div class="relative" data-aos="fade-up" data-aos-delay="400">
              <div
                class="hidden md:block absolute top-5 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white border-4 border-primary-500 z-10"
              ></div>

              <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-12 mb-8 md:mb-0 md:order-2">
                  <div
                    class="bg-white rounded-2xl p-6 shadow-soft inline-block"
                  >
                    <h3
                      class="text-xl font-bold text-gray-900 mb-3 flex items-center"
                    >
                      <div
                        class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-4 flex-shrink-0"
                      >
                        <span class="text-primary-600 font-bold">4</span>
                      </div>
                      <span>Launch & Growth Enablement</span>
                    </h3>
                    <p class="text-gray-600 mb-4">
                      We ensure a smooth launch and provide ongoing support to
                      maximize ROI and drive sustainable growth.
                    </p>
                    <ul class="space-y-2">
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Performance monitoring dashboards, SEO/ASO
                          readiness</span
                        >
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span>A/B tested go-to-market campaigns</span>
                      </li>
                      <li class="flex items-center">
                        <svg
                          class="w-5 h-5 text-primary-500 mr-2 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                        <span
                          >Feedback-driven iterations and lifecycle
                          automation</span
                        >
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="md:w-1/2 md:pl-12 md:order-1">
                  <div class="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=800&q=80"
                      alt="Launch Phase"
                      class="w-full h-64 object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section with Animated Counters -->
    <section
      class="py-16 bg-gradient-purple text-white relative overflow-hidden"
    >
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <div
          class="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-white to-transparent opacity-10"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-white to-transparent opacity-10"
        ></div>

        <!-- Abstract Pattern Background -->
        <div class="absolute inset-0 opacity-10">
          <svg width="100%" height="100%">
            <pattern
              id="pattern-hexagons"
              x="0"
              y="0"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <path
                id="pattern-hexagon"
                d="M20,0 L40,10 L40,30 L20,40 L0,30 L0,10 Z"
                fill="none"
                stroke="white"
                stroke-width="1"
              ></path>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#pattern-hexagons)"
            ></rect>
          </svg>
        </div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- Stat 1 -->
          <div class="text-center" data-aos="fade-up" data-aos-delay="100">
            <div
              class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
                ></path>
              </svg>
            </div>
            <h3 class="text-4xl font-bold mb-2">
              <span class="counter" data-target="500">0</span>+
            </h3>
            <p class="text-white/80">Happy Clients</p>
          </div>

          <!-- Stat 2 -->
          <div class="text-center" data-aos="fade-up" data-aos-delay="200">
            <div
              class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                  clip-rule="evenodd"
                ></path>
                <path
                  d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"
                ></path>
              </svg>
            </div>
            <h3 class="text-4xl font-bold mb-2">
              <span class="counter" data-target="850">0</span>+
            </h3>
            <p class="text-white/80">Projects Completed</p>
          </div>

          <!-- Stat 3 -->
          <div class="text-center" data-aos="fade-up" data-aos-delay="300">
            <div
              class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <h3 class="text-4xl font-bold mb-2">
              <span class="counter" data-target="8">0</span>+
            </h3>
            <p class="text-white/80">Years Experience</p>
          </div>

          <!-- Stat 4 -->
          <div class="text-center" data-aos="fade-up" data-aos-delay="400">
            <div
              class="mb-4 mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
                ></path>
              </svg>
            </div>
            <h3 class="text-4xl font-bold mb-2">
              <span class="counter" data-target="35">0</span>+
            </h3>
            <p class="text-white/80">Expert Team Members</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA and Contact Section -->
    <section id="contact" class="py-20 relative overflow-hidden">
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blobs -->
        <div
          class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-primary-300/30 to-primary-600/30 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-secondary-300/30 to-secondary-600/30 rounded-full filter blur-3xl opacity-30 animate-float"
          style="animation-delay: 1.5s"
        ></div>

        <!-- SVG Pattern Background -->
        <div class="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <pattern
              id="contact-dots"
              x="0"
              y="0"
              width="30"
              height="30"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <circle
                id="contact-dot"
                cx="15"
                cy="15"
                r="1"
                fill="#6d28d9"
              ></circle>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#contact-dots)"
            ></rect>
          </svg>
        </div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Call to Action Section -->
        <div class="relative mb-24" data-aos="fade-up">
          <div
            class="bg-gradient-purple-pink rounded-3xl overflow-hidden shadow-xl"
          >
            <div class="relative p-8 md:p-12 lg:p-16">
              <!-- Background Decorations -->
              <div
                class="absolute top-0 right-0 w-64 h-64 bg-white rounded-full opacity-10 transform translate-x-1/3 -translate-y-1/3"
              ></div>
              <div
                class="absolute bottom-0 left-0 w-64 h-64 bg-white rounded-full opacity-10 transform -translate-x-1/3 translate-y-1/3"
              ></div>

              <!-- Abstract SVG Shape -->
              <svg
                class="absolute bottom-0 right-0 w-80 h-80 text-white opacity-10"
                viewBox="0 0 200 200"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill="currentColor"
                  d="M44.3,-76.5C58.4,-71.2,71.3,-61.3,79.4,-48.1C87.5,-34.8,90.8,-18.4,90.3,-2.4C89.8,13.5,85.6,27.1,77.2,38.2C68.9,49.3,56.3,58,43.1,65.5C29.9,73,14.9,79.4,0.3,78.9C-14.3,78.5,-28.7,71.3,-42.7,63.3C-56.7,55.3,-70.4,46.4,-78.8,33.5C-87.2,20.6,-90.3,3.6,-87.4,-12.1C-84.5,-27.8,-75.5,-42.2,-63.9,-53.4C-52.3,-64.5,-38.1,-72.3,-23.6,-76.9C-9.1,-81.5,5.7,-82.9,19.4,-80.8C33.1,-78.7,45.7,-73.2,44.3,-76.5Z"
                  transform="translate(100 100)"
                />
              </svg>

              <div class="grid grid-cols-1 lg:grid-cols-5 gap-10 items-center">
                <div class="lg:col-span-3">
                  <h2
                    class="text-3xl md:text-4xl font-extrabold text-white mb-6"
                  >
                    Ready to Transform Your Digital Presence?
                  </h2>
                  <p class="text-white/90 text-xl mb-8 max-w-2xl">
                    Partner with RayDesign Technologies to create impactful
                    digital experiences that drive growth, enhance user
                    engagement, and boost your business results.
                  </p>

                  <div class="space-y-4 mb-8">
                    <div class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 mt-1 bg-white/20 rounded-full flex items-center justify-center"
                      >
                        <svg
                          class="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </div>
                      <p class="ml-3 text-white/90">
                        Expert team with proven track record across industries
                      </p>
                    </div>
                    <div class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 mt-1 bg-white/20 rounded-full flex items-center justify-center"
                      >
                        <svg
                          class="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </div>
                      <p class="ml-3 text-white/90">
                        Innovative solutions that keep you ahead of the
                        competition
                      </p>
                    </div>
                    <div class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 mt-1 bg-white/20 rounded-full flex items-center justify-center"
                      >
                        <svg
                          class="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"
                          ></path>
                        </svg>
                      </div>
                      <p class="ml-3 text-white/90">
                        Comprehensive solutions that grow with your business
                      </p>
                    </div>
                  </div>

                  <div class="flex flex-wrap gap-4">
                    <a
                      href="#contactForm"
                      class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-primary-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                    >
                      <span>Get Started Today</span>
                      <svg
                        class="ml-2 -mr-1 w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                          clip-rule="evenodd"
                        ></path>
                      </svg>
                    </a>
                    <a
                      href="tel:+447878361409"
                      class="inline-flex items-center px-6 py-3 text-base font-medium rounded-full text-white border border-white/30 backdrop-blur-sm hover:bg-white/10 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                    >
                      <svg
                        class="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
                        ></path>
                      </svg>
                      <span>Call Us Now</span>
                    </a>
                  </div>
                </div>

                <div class="lg:col-span-2">
                  <div
                    class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20"
                  >
                    <h3 class="text-xl font-bold text-white mb-4">
                      Get a Free Consultation
                    </h3>
                    <p class="text-white/80 mb-6">
                      Schedule a 45-minute consultation with our experts to
                      discuss your project needs.
                    </p>

                    <form class="space-y-4">
                      <div>
                        <label for="quick-name" class="sr-only"
                          >Your Name</label
                        >
                        <input
                          type="text"
                          id="quick-name"
                          name="quick-name"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                          placeholder="Your Name"
                        />
                      </div>
                      <div>
                        <label for="quick-email" class="sr-only"
                          >Your Email</label
                        >
                        <input
                          type="email"
                          id="quick-email"
                          name="quick-email"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                          placeholder="Your Email"
                        />
                      </div>
                      <div>
                        <label for="quick-service" class="sr-only"
                          >Service Interested In</label
                        >
                        <select
                          id="quick-service"
                          name="quick-service"
                          class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                        >
                          <option
                            value=""
                            disabled
                            selected
                            class="text-gray-500"
                          >
                            Service Interested In
                          </option>
                          <option value="web-development" class="text-gray-800">
                            Web Development
                          </option>
                          <option value="mobile-app" class="text-gray-800">
                            Mobile App Development
                          </option>
                          <option value="ui-ux" class="text-gray-800">
                            UI/UX Design
                          </option>
                          <option
                            value="digital-marketing"
                            class="text-gray-800"
                          >
                            Digital Marketing
                          </option>
                          <option value="ai-solutions" class="text-gray-800">
                            AI Solutions
                          </option>
                          <option value="other" class="text-gray-800">
                            Other Services
                          </option>
                        </select>
                      </div>

                      <button
                        type="submit"
                        class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium rounded-lg text-primary-700 bg-white shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                      >
                        Book My Free Consultation
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Wave Separator -->
          <svg
            class="absolute -bottom-1 left-0 w-full text-white"
            viewBox="0 0 1440 96"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0,96L60,80C120,64,240,32,360,32C480,32,600,64,720,64C840,64,960,32,1080,24C1200,16,1320,32,1380,40L1440,48L1440,96L1380,96C1320,96,1200,96,1080,96C960,96,840,96,720,96C600,96,480,96,360,96C240,96,120,96,60,96L0,96Z"
              fill="currentColor"
            ></path>
          </svg>
        </div>

        <!-- Contact Us Section -->
        <div
          id="contactForm"
          class="mt-16"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          <div class="text-center max-w-3xl mx-auto mb-16">
            <span
              class="inline-block px-3 py-1 text-xs font-semibold tracking-wider text-primary-700 uppercase rounded-full bg-primary-100 mb-4"
            >
              Get In Touch
            </span>
            <h2 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4">
              Let's Discuss Your <span class="text-gradient">Project</span>
            </h2>
            <p class="text-xl text-gray-600">
              Fill out the form below, and our team will get back to you within
              24 hours to discuss how we can help bring your vision to life.
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
            <!-- Contact Form -->
            <div class="lg:col-span-2">
              <div
                class="bg-white rounded-2xl shadow-soft p-8 border border-gray-100"
              >
                <form class="space-y-6">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label
                        for="name"
                        class="block text-sm font-medium text-gray-700 mb-1"
                        >Your Name</label
                      >
                      <input
                        type="text"
                        id="name"
                        name="name"
                        class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                        placeholder="John Smith"
                      />
                    </div>
                    <div>
                      <label
                        for="email"
                        class="block text-sm font-medium text-gray-700 mb-1"
                        >Email Address</label
                      >
                      <input
                        type="email"
                        id="email"
                        name="email"
                        class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label
                        for="phone"
                        class="block text-sm font-medium text-gray-700 mb-1"
                        >Phone Number</label
                      >
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                        placeholder="+44 7700 900000"
                      />
                    </div>
                    <div>
                      <label
                        for="company"
                        class="block text-sm font-medium text-gray-700 mb-1"
                        >Company Name</label
                      >
                      <input
                        type="text"
                        id="company"
                        name="company"
                        class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                        placeholder="Your Company Ltd"
                      />
                    </div>
                  </div>

                  <div>
                    <label
                      for="service"
                      class="block text-sm font-medium text-gray-700 mb-1"
                      >Service You're Interested In</label
                    >
                    <select
                      id="service"
                      name="service"
                      class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                    >
                      <option value="" disabled selected>
                        Select a Service
                      </option>
                      <option value="web-development">
                        Web Design & Development
                      </option>
                      <option value="mobile-app">Mobile App Development</option>
                      <option value="ecommerce">E-Commerce Solutions</option>
                      <option value="ui-ux">UI/UX Design</option>
                      <option value="branding">Branding & Identity</option>
                      <option value="digital-marketing">
                        Digital Marketing
                      </option>
                      <option value="seo">SEO Services</option>
                      <option value="ppc">PPC Advertising</option>
                      <option value="social-media">
                        Social Media Management
                      </option>
                      <option value="ai-solutions">
                        AI & Automation Solutions
                      </option>
                      <option value="other">Other Services</option>
                    </select>
                  </div>

                  <div>
                    <label
                      for="budget"
                      class="block text-sm font-medium text-gray-700 mb-1"
                      >Project Budget</label
                    >
                    <select
                      id="budget"
                      name="budget"
                      class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                    >
                      <option value="" disabled selected>
                        Select Budget Range
                      </option>
                      <option value="less-5k">Less than £5,000</option>
                      <option value="5k-10k">£5,000 - £10,000</option>
                      <option value="10k-25k">£10,000 - £25,000</option>
                      <option value="25k-50k">£25,000 - £50,000</option>
                      <option value="50k-100k">£50,000 - £100,000</option>
                      <option value="more-100k">More than £100,000</option>
                      <option value="not-sure">Not sure yet</option>
                    </select>
                  </div>

                  <div>
                    <label
                      for="message"
                      class="block text-sm font-medium text-gray-700 mb-1"
                      >Project Details</label
                    >
                    <textarea
                      id="message"
                      name="message"
                      rows="5"
                      class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-300"
                      placeholder="Tell us about your project, goals, and requirements..."
                    ></textarea>
                  </div>

                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="terms"
                        name="terms"
                        type="checkbox"
                        class="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="terms" class="text-gray-600"
                        >I agree to the
                        <a
                          href="#"
                          class="text-primary-600 hover:text-primary-700"
                          >Privacy Policy</a
                        >
                        and
                        <a
                          href="#"
                          class="text-primary-600 hover:text-primary-700"
                          >Terms of Service</a
                        ></label
                      >
                    </div>
                  </div>

                  <div>
                    <button
                      type="submit"
                      class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium rounded-lg text-white bg-gradient-purple shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <span>Submit Inquiry</span>
                      <svg
                        class="ml-2 -mr-1 w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                          clip-rule="evenodd"
                        ></path>
                      </svg>
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <!-- Contact Information -->
            <div>
              <div
                class="bg-white rounded-2xl shadow-soft p-8 border border-gray-100 mb-8"
              >
                <h3 class="text-xl font-bold text-gray-900 mb-6">
                  Contact Information
                </h3>

                <div class="space-y-6">
                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-primary-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-semibold text-gray-900">
                        Phone
                      </h4>
                      <p class="text-gray-600 mt-1">
                        UK:
                        <a
                          href="tel:+447878361409"
                          class="text-primary-600 hover:text-primary-700"
                          >+44 7878 361409</a
                        >
                      </p>
                      <!-- <p class="text-gray-600">
                        US:
                        <a
                          href="tel:+17862337886"
                          class="text-primary-600 hover:text-primary-700"
                        </a>
                        >
                      </p> -->
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-primary-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-semibold text-gray-900">
                        Email
                      </h4>
                      <p class="text-gray-600 mt-1">
                        General Inquiries:
                        <a
                          href="mailto:<EMAIL>"
                          class="text-primary-600 hover:text-primary-700"
                          ><EMAIL></a
                        >
                      </p>
                      <p class="text-gray-600">
                        Support Requests:
                        <a
                          href="mailto:<EMAIL>"
                          class="text-primary-600 hover:text-primary-700"
                          ><EMAIL></a
                        >
                      </p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-primary-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                          ></path>
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-semibold text-gray-900">
                        Address
                      </h4>
                      <p class="text-gray-600 mt-1">1-A Edmundson Street,</p>
                      <p class="text-gray-600">Blackburn BB2 1HL,</p>
                      <p class="text-gray-600">United Kingdom</p>
                    </div>
                  </div>

                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-primary-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-semibold text-gray-900">
                        Business Hours
                      </h4>
                      <p class="text-gray-600 mt-1">
                        Monday - Friday: 08:30 AM - 06:00 PM (GMT)
                      </p>
                      <p class="text-gray-600">
                        Emergency Support Available 24/7
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Social Media -->
              <div
                class="bg-white rounded-2xl shadow-soft p-8 border border-gray-100"
              >
                <h3 class="text-xl font-bold text-gray-900 mb-6">
                  Connect With Us
                </h3>

                <div class="grid grid-cols-4 gap-4">
                  <a
                    href="#"
                    class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group"
                  >
                    <i
                      class="fab fa-linkedin text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                    ></i>
                    <span
                      class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                      >LinkedIn</span
                    >
                  </a>
                  <a
                    href="#"
                    class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group"
                  >
                    <i
                      class="fab fa-twitter text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                    ></i>
                    <span
                      class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                      >Twitter</span
                    >
                  </a>
                  <a
                    href="#"
                    class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group"
                  >
                    <i
                      class="fab fa-facebook text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                    ></i>
                    <span
                      class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                      >Facebook</span
                    >
                  </a>
                  <a
                    href="#"
                    class="flex flex-col items-center justify-center p-4 rounded-xl bg-gray-50 hover:bg-primary-50 transition-colors duration-300 group"
                  >
                    <i
                      class="fab fa-instagram text-2xl text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                    ></i>
                    <span
                      class="text-xs mt-2 text-gray-500 group-hover:text-primary-600 transition-colors duration-300"
                      >Instagram</span
                    >
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Google Map -->
    <div class="w-full h-96 relative mt-16">
      <iframe
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2362.1054555573354!2d-2.4908899842347944!3d53.748943980074004!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x487b9f6cf9e71a1d%3A0x7f0e9f7a79eb2b31!2sEdmundson%20St%2C%20Blackburn%20BB2%201HL%2C%20UK!5e0!3m2!1sen!2sus!4v1631289726754!5m2!1sen!2sus"
        width="100%"
        height="100%"
        style="border: 0"
        allowfullscreen=""
        loading="lazy"
        class="absolute top-0 left-0 w-full h-full z-0"
      ></iframe>

      <div
        class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 w-full max-w-md px-4"
      >
        <div class="bg-white rounded-xl shadow-xl p-6 backdrop-blur-md">
          <div class="flex items-center">
            <div
              class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center flex-shrink-0"
            >
              <svg
                class="w-6 h-6 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                ></path>
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                ></path>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-semibold text-gray-900">
                RayDesign Technologies
              </h3>
              <p class="text-gray-600 text-sm">
                1-A Edmundson Street, Blackburn BB2 1HL, United Kingdom
              </p>
            </div>
          </div>
          <div class="mt-4 flex space-x-2">
            <a
              href="https://www.google.com/maps/dir//Edmundson+St,+Blackburn+BB2+1HL,+UK/"
              target="_blank"
              class="flex-1 inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-300"
            >
              <svg
                class="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                ></path>
              </svg>
              Get Directions
            </a>
            <a
              href="tel:+447878361409"
              class="inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-lg text-primary-600 bg-primary-50 hover:bg-primary-100 transition-colors duration-300"
            >
              <svg
                class="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                ></path>
              </svg>
              Call
            </a>
          </div>
        </div>
      </div>
    </div>
    <!-- Footer Section with Comprehensive Information -->
    <footer
      class="bg-gradient-to-b from-gray-50 to-gray-100 pt-20 pb-10 relative overflow-hidden"
    >
      <!-- Background Decoration Elements -->
      <div class="absolute inset-0 z-0">
        <!-- Animated Gradient Blobs -->
        <div
          class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-primary-300/10 to-primary-600/10 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"
        ></div>
        <div
          class="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-tr from-secondary-300/10 to-secondary-600/10 rounded-full filter blur-3xl opacity-30 animate-float"
          style="animation-delay: 2s"
        ></div>

        <!-- SVG Pattern Background -->
        <div class="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <pattern
              id="footer-dots"
              x="0"
              y="0"
              width="30"
              height="30"
              patternUnits="userSpaceOnUse"
              patternContentUnits="userSpaceOnUse"
            >
              <circle
                id="footer-dot"
                cx="15"
                cy="15"
                r="1"
                fill="#6d28d9"
              ></circle>
            </pattern>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#footer-dots)"
            ></rect>
          </svg>
        </div>
      </div>

      <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Footer Top Section -->
        <div
          class="grid grid-cols-1 lg:grid-cols-5 gap-10 pb-10 border-b border-gray-200"
        >
          <!-- Company Information -->
          <div class="lg:col-span-2">
            <a href="/" class="inline-block mb-6">
              <img
                class="h-16 w-auto"
                src="https://i.postimg.cc/cCwXxx3N/Ray-Design-Logo.png"
                alt="RayDesign Technologies Logo"
              />
            </a>
            <p class="text-gray-600 mb-6 max-w-md">
              RayDesign Technologies is a future-forging innovation powerhouse
              that merges design thinking, intelligent automation, full-stack
              engineering, and marketing science to architect high-impact,
              human-centered digital ecosystems.
            </p>
            <div class="flex space-x-4 mb-6">
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-facebook-f"></i>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-twitter"></i>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-linkedin-in"></i>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-instagram"></i>
              </a>
              <a
                href="#"
                class="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center text-gray-600 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-300"
              >
                <i class="fab fa-youtube"></i>
              </a>
            </div>
            <div class="bg-white rounded-xl shadow-sm p-4 flex items-center">
              <div class="flex-shrink-0 mr-4">
                <div
                  class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-primary-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div>
                <p class="text-sm text-gray-500">
                  Have a question? Call us now
                </p>
                <a
                  href="tel:+447878361409"
                  class="text-lg font-semibold text-primary-600 hover:text-primary-700"
                  >+44 7878 361409</a
                >
              </div>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-lg font-bold text-gray-900 mb-6">Quick Links</h3>
            <ul class="space-y-3">
              <li>
                <a
                  href="/"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Home
                </a>
              </li>
              <li>
                <a
                  href="/about.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  About Us
                </a>
              </li>
              <li></li>
              <li>
                <a
                  href="/portfolio.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Portfolio
                </a>
              </li>

              <li>
                <a
                  href="/contact.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Contact Us
                </a>
              </li>
              <li>
                <a
                  href=""
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  Careers
                </a>
              </li>
            </ul>
          </div>

          <!-- Services -->
          <div>
            <h3 class="text-lg font-bold text-gray-900 mb-6">Our Services</h3>
            <ul class="space-y-3">
              <li>
                <a
                  href="/services/ai-solutions.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  AI & Automation
                </a>
              </li>
              <li>
                <a
                  href="/services/branding.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Branding & Identity
                </a>
              </li>
              <li>
                <a
                  href="/services/e-commerce.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  E-Commerce Solutions
                </a>
              </li>
              <li>
                <a
                  href="/services/mobile-apps.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Mobile App Development
                </a>
              </li>
              <li>
                <a
                  href="/services/ppc.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Pay-Per-Click (PPC) Ads
                </a>
              </li>
              <li>
                <a
                  href="/services/seo.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  SEO Services
                </a>
              </li>
              <li>
                <a
                  href="/services/social-media.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Social Media Management
                </a>
              </li>
              <li>
                <a
                  href="/services/ui-ux-design.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  UI/UX Design
                </a>
              </li>
              <li>
                <a
                  href="/services/web-development.html"
                  class="text-gray-600 hover:text-primary-600 transition-colors duration-300 flex items-center"
                >
                  <svg
                    class="w-4 h-4 mr-2 text-primary-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Web Development
                </a>
              </li>
            </ul>
          </div>

          <!-- Newsletter Signup -->
          <div>
            <h3 class="text-lg font-bold text-gray-900 mb-6">
              Subscribe to Our Newsletter
            </h3>
            <p class="text-gray-600 mb-4">
              Stay updated with our latest news, tips, and special offers.
            </p>
            <form class="space-y-3">
              <div>
                <label for="footer-email" class="sr-only">Email Address</label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <svg
                      class="w-5 h-5 text-gray-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
                      ></path>
                      <path
                        d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
                      ></path>
                    </svg>
                  </div>
                  <input
                    type="email"
                    id="footer-email"
                    name="email"
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white"
                    placeholder="Your email address"
                  />
                </div>
              </div>
              <button
                type="submit"
                class="w-full inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-lg text-white bg-gradient-purple shadow-sm hover:shadow-md transform transition-all duration-300 hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"
                  ></path>
                </svg>
                Subscribe
              </button>
            </form>

            <!-- Industry Badges -->
            <div class="mt-6">
              <p class="text-sm text-gray-500 mb-3">Recognized By:</p>
              <div class="flex flex-wrap gap-3">
                <p>Google Verified Business</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer Middle Section - Industries We Serve -->
        <div class="py-10 border-b border-gray-200">
          <h3 class="text-lg font-bold text-gray-900 mb-6">
            Industries We Serve
          </h3>
          <div
            class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8 gap-4"
          >
            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Retail & eCommerce</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Healthcare</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Finance & Fintech</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Education</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Real Estate</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Travel & Hospitality</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"
                  ></path>
                  <path
                    d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1v-5a1 1 0 00-.293-.707l-2-2A1 1 0 0015 7h-1z"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Logistics</span
              >
            </a>

            <a
              href="#"
              class="flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:bg-primary-50 hover:shadow-md transition-all duration-300 group"
            >
              <div
                class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mb-3 group-hover:bg-primary-200 transition-colors duration-300"
              >
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <span
                class="text-sm font-medium text-gray-700 group-hover:text-primary-700 transition-colors duration-300"
                >Legal & Compliance</span
              >
            </a>
          </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="pt-10">
          <div
            class="flex flex-col md:flex-row md:justify-between md:items-center"
          >
            <div class="mb-6 md:mb-0">
              <p class="text-sm text-gray-500">
                &copy; 2023 RayDesign Technologies. All rights reserved.
              </p>
            </div>

            <div class="flex flex-col md:flex-row gap-4 md:gap-8">
              <a
                href="privacy-policy.html"
                class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300"
                >Privacy Policy</a
              >
              <a
                href="terms-of-service.html"
                class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300"
                >Terms of Service</a
              >
              <a
                href="cookie-policy.html"
                class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300"
                >Cookie Policy</a
              >
              <a
                href="sitemap.html"
                class="text-sm text-gray-500 hover:text-primary-600 transition-colors duration-300"
                >Sitemap</a
              >
            </div>
          </div>

          <div
            class="mt-6 flex flex-col md:flex-row md:justify-between md:items-center"
          >
            <div class="mb-4 md:mb-0">
              <p class="text-xs text-gray-400">
                RayDesign Technologies is registered in the United Kingdom.
                Company No: 12345678
              </p>
            </div>

            <div>
              <p class="text-xs text-gray-400">
                Designed and developed with
                <span class="text-red-500">❤</span> by RayDesign Technologies
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Back to Top Button -->
    <button
      id="backToTop"
      class="fixed bottom-6 right-6 w-12 h-12 rounded-full bg-primary-600 text-white shadow-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 flex items-center justify-center transform scale-0 opacity-0"
    >
      <svg
        class="w-6 h-6"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 10l7-7m0 0l7 7m-7-7v18"
        ></path>
      </svg>
    </button>

    <!-- Cookie Consent -->
    <div
      id="cookieConsent"
      class="fixed bottom-0 left-0 w-full bg-white shadow-lg transform translate-y-full transition-transform duration-500 z-50"
    >
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div
          class="flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div class="mb-4 md:mb-0 md:mr-8">
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-0.5">
                <svg
                  class="w-6 h-6 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"
                  ></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-lg font-medium text-gray-900">Cookie Notice</h3>
                <p class="mt-1 text-sm text-gray-600">
                  We use cookies to enhance your browsing experience, analyze
                  site traffic, and personalize content. By clicking "Accept All
                  Cookies", you agree to the storing of cookies on your device.
                </p>
                <div class="mt-2">
                  <a
                    href="cookie-policy.html"
                    class="text-sm font-medium text-primary-600 hover:text-primary-700"
                    >Learn more about our Cookie Policy</a
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-col sm:flex-row gap-3">
            <button
              id="cookieSettings"
              class="inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cookie Settings
            </button>
            <button
              id="acceptCookies"
              class="inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Accept All Cookies
            </button>
            <button
              id="declineCookies"
              class="inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Decline Non-Essential
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Initialize AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Main JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize AOS
        AOS.init({
          duration: 800,
          easing: "ease-in-out",
          once: true,
          mirror: false,
        });

        // Back to Top Button
        const backToTopButton = document.getElementById("backToTop");

        window.addEventListener("scroll", () => {
          if (window.pageYOffset > 300) {
            backToTopButton.classList.remove("scale-0", "opacity-0");
            backToTopButton.classList.add("scale-100", "opacity-100");
          } else {
            backToTopButton.classList.remove("scale-100", "opacity-100");
            backToTopButton.classList.add("scale-0", "opacity-0");
          }
        });

        backToTopButton.addEventListener("click", () => {
          window.scrollTo({
            top: 0,
            behavior: "smooth",
          });
        });

        // Cookie Consent
        const cookieConsent = document.getElementById("cookieConsent");
        const acceptCookies = document.getElementById("acceptCookies");
        const declineCookies = document.getElementById("declineCookies");
        const cookieSettings = document.getElementById("cookieSettings");

        // Check if user has already made a cookie choice
        const cookieChoice = localStorage.getItem("cookieChoice");

        if (!cookieChoice) {
          // Show cookie consent after 2 seconds
          setTimeout(() => {
            cookieConsent.classList.remove("translate-y-full");
            cookieConsent.classList.add("translate-y-0");
          }, 2000);
        }

        acceptCookies.addEventListener("click", () => {
          localStorage.setItem("cookieChoice", "accepted");
          cookieConsent.classList.remove("translate-y-0");
          cookieConsent.classList.add("translate-y-full");
        });

        declineCookies.addEventListener("click", () => {
          localStorage.setItem("cookieChoice", "declined");
          cookieConsent.classList.remove("translate-y-0");
          cookieConsent.classList.add("translate-y-full");
        });

        cookieSettings.addEventListener("click", () => {
          // Implement cookie settings modal or redirect to cookie policy page
          window.location.href = "cookie-policy.html";
        });
      });
    </script>
    <!-- JavaScript for Animated Counters -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const counters = document.querySelectorAll(".counter");
        const speed = 200;

        const animateCounter = (counter) => {
          const target = +counter.dataset.target;
          let count = 0;
          const inc = target / speed;

          const updateCount = () => {
            if (count < target) {
              count += inc;
              counter.innerText = Math.ceil(count);
              setTimeout(updateCount, 1);
            } else {
              counter.innerText = target;
            }
          };

          updateCount();
        };

        // Use Intersection Observer to trigger counter animation when visible
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                const counter = entry.target;
                animateCounter(counter);
                observer.unobserve(counter);
              }
            });
          },
          { threshold: 0.2 }
        );

        counters.forEach((counter) => {
          observer.observe(counter);
        });
      });
    </script>
    <!-- JavaScript for Services Section -->
    <script>
      // Filter functionality for service cards
      document.addEventListener("DOMContentLoaded", function () {
        const filterButtons = document.querySelectorAll(".service-filter-btn");
        const serviceCards = document.querySelectorAll(".service-card");

        filterButtons.forEach((button) => {
          button.addEventListener("click", () => {
            // Remove active class from all buttons
            filterButtons.forEach((btn) => {
              btn.classList.remove("active", "bg-primary-600", "text-white");
              btn.classList.add("bg-white", "text-gray-700");
            });

            // Add active class to clicked button
            button.classList.add("active", "bg-primary-600", "text-white");
            button.classList.remove("bg-white", "text-gray-700");

            const filter = button.getAttribute("data-filter");

            // Show/hide cards based on filter
            serviceCards.forEach((card) => {
              if (
                filter === "all" ||
                card.getAttribute("data-category") === filter
              ) {
                card.style.display = "block";
              } else {
                card.style.display = "none";
              }
            });
          });
        });
      });
      // Create cursor element
      const cursor = document.createElement("div");
      cursor.classList.add("custom-cursor");
      document.body.appendChild(cursor);

      // Add styles
      const style = document.createElement("style");
      style.innerHTML = `
  body {
    cursor: none;
  }

  .custom-cursor {
    position: fixed;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    pointer-events: none;
    transform: translate(-50%, -50%);
    z-index: 9999;

    /* Blue-Cyan-Purple Gradient */
    background: linear-gradient(
      135deg,
      rgba(0, 255, 255, 0.9) 0%,
      rgba(0, 128, 255, 0.9) 30%,
      rgba(128, 0, 255, 0.9) 60%,
      rgba(255, 0, 255, 0.9) 100%
    );

    box-shadow:
      0 0 6px rgba(0, 255, 255, 0.7),
      0 0 12px rgba(0, 128, 255, 0.6),
      0 0 20px rgba(128, 0, 255, 0.8);

    transition: all 0.2s ease;
    animation: rainbow-rotate 5s linear infinite, pulse-fade 2s ease-in-out infinite;
  }

  @keyframes rainbow-rotate {
    0% {
      filter: hue-rotate(0deg);
    }
    100% {
      filter: hue-rotate(360deg);
    }
  }

  @keyframes pulse-fade {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.08);
      opacity: 0.85;
    }
  }

  .custom-cursor.hollow {
    width: 35px;
    height: 35px;
    background: transparent;
    border: 2px solid rgba(0, 200, 255, 0.8);
    box-shadow:
      0 0 10px rgba(0, 128, 255, 0.7),
      inset 0 0 10px rgba(128, 0, 255, 0.5);
    animation: pulse 1.5s ease-in-out infinite alternate;
  }

  @keyframes pulse {
    0% {
      box-shadow:
        0 0 10px rgba(0, 128, 255, 0.7),
        inset 0 0 10px rgba(128, 0, 255, 0.5);
    }
    100% {
      box-shadow:
        0 0 15px rgba(0, 200, 255, 0.8),
        inset 0 0 15px rgba(181, 0, 255, 0.6);
    }
  }
`;

      document.head.appendChild(style);

      // Track mouse movement with light smoothing
      let mouseX = 0,
        mouseY = 0;
      let cursorX = 0,
        cursorY = 0;
      const smoothing = 0.2;

      document.addEventListener("mousemove", (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
      });

      // Check proximity to headings
      function checkHeadingProximity() {
        const headings = document.querySelectorAll("h1, h2, h3, h4, h5, h6");
        let nearHeading = false;

        headings.forEach((heading) => {
          const rect = heading.getBoundingClientRect();

          // Create a slightly larger detection area around the heading
          const expandedRect = {
            left: rect.left - 10,
            right: rect.right + 10,
            top: rect.top - 10,
            bottom: rect.bottom + 10,
          };

          // Check if cursor is near the heading
          if (
            mouseX >= expandedRect.left &&
            mouseX <= expandedRect.right &&
            mouseY >= expandedRect.top &&
            mouseY <= expandedRect.bottom
          ) {
            nearHeading = true;
          }
        });

        // Update cursor appearance based on proximity
        if (nearHeading) {
          cursor.classList.add("hollow");
        } else {
          cursor.classList.remove("hollow");
        }
      }

      // Animation loop for smooth movement and heading detection
      function animate() {
        // Smooth cursor movement
        cursorX += (mouseX - cursorX) * smoothing;
        cursorY += (mouseY - cursorY) * smoothing;

        // Update cursor position
        cursor.style.left = `${cursorX}px`;
        cursor.style.top = `${cursorY}px`;

        // Check if near headings
        checkHeadingProximity();

        // Continue animation
        requestAnimationFrame(animate);
      }

      // Start animation
      animate();

      // Hide default cursor when page loads
      document.addEventListener("DOMContentLoaded", () => {
        setTimeout(() => {
          document.body.style.cursor = "none";
        }, 100);
      });
    </script>
  </body>
</html>
